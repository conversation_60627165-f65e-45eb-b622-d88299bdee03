/*-------------------------------------------------------------------------------------
 *
 * Copyright (c) Microsoft Corporation
 * Licensed under the MIT license
 *
 *-------------------------------------------------------------------------------------*/
import "oaidl.idl";
import "ocidl.idl";

import "dxgicommon.idl";
import "dxgiformat.idl";

import "d3dcommon.idl";

cpp_quote("#include <winapifamily.h>")

#pragma region App Family
cpp_quote("#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_APP | WINAPI_PARTITION_GAMES)")

// NOTE: The following constants are generated from the D3D12 hardware spec.  Do not edit these values directly.
cpp_quote( "#ifndef _D3D12_CONSTANTS" )
cpp_quote( "#define _D3D12_CONSTANTS" )
const UINT D3D12_16BIT_INDEX_STRIP_CUT_VALUE = 0xffff;
const UINT D3D12_32BIT_INDEX_STRIP_CUT_VALUE = 0xffffffff;
const UINT D3D12_8BIT_INDEX_STRIP_CUT_VALUE = 0xff;
const UINT D3D12_APPEND_ALIGNED_ELEMENT = 0xffffffff;
const UINT D3D12_ARRAY_AXIS_ADDRESS_RANGE_BIT_COUNT = 9;
const UINT D3D12_CLIP_OR_CULL_DISTANCE_COUNT = 8;
const UINT D3D12_CLIP_OR_CULL_DISTANCE_ELEMENT_COUNT = 2;
const UINT D3D12_COMMONSHADER_CONSTANT_BUFFER_API_SLOT_COUNT = 14;
const UINT D3D12_COMMONSHADER_CONSTANT_BUFFER_COMPONENTS = 4;
const UINT D3D12_COMMONSHADER_CONSTANT_BUFFER_COMPONENT_BIT_COUNT = 32;
const UINT D3D12_COMMONSHADER_CONSTANT_BUFFER_HW_SLOT_COUNT = 15;
const UINT D3D12_COMMONSHADER_CONSTANT_BUFFER_PARTIAL_UPDATE_EXTENTS_BYTE_ALIGNMENT = 16;
const UINT D3D12_COMMONSHADER_CONSTANT_BUFFER_REGISTER_COMPONENTS = 4;
const UINT D3D12_COMMONSHADER_CONSTANT_BUFFER_REGISTER_COUNT = 15;
const UINT D3D12_COMMONSHADER_CONSTANT_BUFFER_REGISTER_READS_PER_INST = 1;
const UINT D3D12_COMMONSHADER_CONSTANT_BUFFER_REGISTER_READ_PORTS = 1;
const UINT D3D12_COMMONSHADER_FLOWCONTROL_NESTING_LIMIT = 64;
const UINT D3D12_COMMONSHADER_IMMEDIATE_CONSTANT_BUFFER_REGISTER_COMPONENTS = 4;
const UINT D3D12_COMMONSHADER_IMMEDIATE_CONSTANT_BUFFER_REGISTER_COUNT = 1;
const UINT D3D12_COMMONSHADER_IMMEDIATE_CONSTANT_BUFFER_REGISTER_READS_PER_INST = 1;
const UINT D3D12_COMMONSHADER_IMMEDIATE_CONSTANT_BUFFER_REGISTER_READ_PORTS = 1;
const UINT D3D12_COMMONSHADER_IMMEDIATE_VALUE_COMPONENT_BIT_COUNT = 32;
const UINT D3D12_COMMONSHADER_INPUT_RESOURCE_REGISTER_COMPONENTS = 1;
const UINT D3D12_COMMONSHADER_INPUT_RESOURCE_REGISTER_COUNT = 128;
const UINT D3D12_COMMONSHADER_INPUT_RESOURCE_REGISTER_READS_PER_INST = 1;
const UINT D3D12_COMMONSHADER_INPUT_RESOURCE_REGISTER_READ_PORTS = 1;
const UINT D3D12_COMMONSHADER_INPUT_RESOURCE_SLOT_COUNT = 128;
const UINT D3D12_COMMONSHADER_SAMPLER_REGISTER_COMPONENTS = 1;
const UINT D3D12_COMMONSHADER_SAMPLER_REGISTER_COUNT = 16;
const UINT D3D12_COMMONSHADER_SAMPLER_REGISTER_READS_PER_INST = 1;
const UINT D3D12_COMMONSHADER_SAMPLER_REGISTER_READ_PORTS = 1;
const UINT D3D12_COMMONSHADER_SAMPLER_SLOT_COUNT = 16;
const UINT D3D12_COMMONSHADER_SUBROUTINE_NESTING_LIMIT = 32;
const UINT D3D12_COMMONSHADER_TEMP_REGISTER_COMPONENTS = 4;
const UINT D3D12_COMMONSHADER_TEMP_REGISTER_COMPONENT_BIT_COUNT = 32;
const UINT D3D12_COMMONSHADER_TEMP_REGISTER_COUNT = 4096;
const UINT D3D12_COMMONSHADER_TEMP_REGISTER_READS_PER_INST = 3;
const UINT D3D12_COMMONSHADER_TEMP_REGISTER_READ_PORTS = 3;
const UINT D3D12_COMMONSHADER_TEXCOORD_RANGE_REDUCTION_MAX = 10;
const INT D3D12_COMMONSHADER_TEXCOORD_RANGE_REDUCTION_MIN = -10;
const INT D3D12_COMMONSHADER_TEXEL_OFFSET_MAX_NEGATIVE = -8;
const UINT D3D12_COMMONSHADER_TEXEL_OFFSET_MAX_POSITIVE = 7;
const UINT D3D12_CONSTANT_BUFFER_DATA_PLACEMENT_ALIGNMENT = 256;
const UINT D3D12_CS_4_X_BUCKET00_MAX_BYTES_TGSM_WRITABLE_PER_THREAD = 256;
const UINT D3D12_CS_4_X_BUCKET00_MAX_NUM_THREADS_PER_GROUP = 64;
const UINT D3D12_CS_4_X_BUCKET01_MAX_BYTES_TGSM_WRITABLE_PER_THREAD = 240;
const UINT D3D12_CS_4_X_BUCKET01_MAX_NUM_THREADS_PER_GROUP = 68;
const UINT D3D12_CS_4_X_BUCKET02_MAX_BYTES_TGSM_WRITABLE_PER_THREAD = 224;
const UINT D3D12_CS_4_X_BUCKET02_MAX_NUM_THREADS_PER_GROUP = 72;
const UINT D3D12_CS_4_X_BUCKET03_MAX_BYTES_TGSM_WRITABLE_PER_THREAD = 208;
const UINT D3D12_CS_4_X_BUCKET03_MAX_NUM_THREADS_PER_GROUP = 76;
const UINT D3D12_CS_4_X_BUCKET04_MAX_BYTES_TGSM_WRITABLE_PER_THREAD = 192;
const UINT D3D12_CS_4_X_BUCKET04_MAX_NUM_THREADS_PER_GROUP = 84;
const UINT D3D12_CS_4_X_BUCKET05_MAX_BYTES_TGSM_WRITABLE_PER_THREAD = 176;
const UINT D3D12_CS_4_X_BUCKET05_MAX_NUM_THREADS_PER_GROUP = 92;
const UINT D3D12_CS_4_X_BUCKET06_MAX_BYTES_TGSM_WRITABLE_PER_THREAD = 160;
const UINT D3D12_CS_4_X_BUCKET06_MAX_NUM_THREADS_PER_GROUP = 100;
const UINT D3D12_CS_4_X_BUCKET07_MAX_BYTES_TGSM_WRITABLE_PER_THREAD = 144;
const UINT D3D12_CS_4_X_BUCKET07_MAX_NUM_THREADS_PER_GROUP = 112;
const UINT D3D12_CS_4_X_BUCKET08_MAX_BYTES_TGSM_WRITABLE_PER_THREAD = 128;
const UINT D3D12_CS_4_X_BUCKET08_MAX_NUM_THREADS_PER_GROUP = 128;
const UINT D3D12_CS_4_X_BUCKET09_MAX_BYTES_TGSM_WRITABLE_PER_THREAD = 112;
const UINT D3D12_CS_4_X_BUCKET09_MAX_NUM_THREADS_PER_GROUP = 144;
const UINT D3D12_CS_4_X_BUCKET10_MAX_BYTES_TGSM_WRITABLE_PER_THREAD = 96;
const UINT D3D12_CS_4_X_BUCKET10_MAX_NUM_THREADS_PER_GROUP = 168;
const UINT D3D12_CS_4_X_BUCKET11_MAX_BYTES_TGSM_WRITABLE_PER_THREAD = 80;
const UINT D3D12_CS_4_X_BUCKET11_MAX_NUM_THREADS_PER_GROUP = 204;
const UINT D3D12_CS_4_X_BUCKET12_MAX_BYTES_TGSM_WRITABLE_PER_THREAD = 64;
const UINT D3D12_CS_4_X_BUCKET12_MAX_NUM_THREADS_PER_GROUP = 256;
const UINT D3D12_CS_4_X_BUCKET13_MAX_BYTES_TGSM_WRITABLE_PER_THREAD = 48;
const UINT D3D12_CS_4_X_BUCKET13_MAX_NUM_THREADS_PER_GROUP = 340;
const UINT D3D12_CS_4_X_BUCKET14_MAX_BYTES_TGSM_WRITABLE_PER_THREAD = 32;
const UINT D3D12_CS_4_X_BUCKET14_MAX_NUM_THREADS_PER_GROUP = 512;
const UINT D3D12_CS_4_X_BUCKET15_MAX_BYTES_TGSM_WRITABLE_PER_THREAD = 16;
const UINT D3D12_CS_4_X_BUCKET15_MAX_NUM_THREADS_PER_GROUP = 768;
const UINT D3D12_CS_4_X_DISPATCH_MAX_THREAD_GROUPS_IN_Z_DIMENSION = 1;
const UINT D3D12_CS_4_X_RAW_UAV_BYTE_ALIGNMENT = 256;
const UINT D3D12_CS_4_X_THREAD_GROUP_MAX_THREADS_PER_GROUP = 768;
const UINT D3D12_CS_4_X_THREAD_GROUP_MAX_X = 768;
const UINT D3D12_CS_4_X_THREAD_GROUP_MAX_Y = 768;
const UINT D3D12_CS_4_X_UAV_REGISTER_COUNT = 1;
const UINT D3D12_CS_DISPATCH_MAX_THREAD_GROUPS_PER_DIMENSION = 65535;
const UINT D3D12_CS_TGSM_REGISTER_COUNT = 8192;
const UINT D3D12_CS_TGSM_REGISTER_READS_PER_INST = 1;
const UINT D3D12_CS_TGSM_RESOURCE_REGISTER_COMPONENTS = 1;
const UINT D3D12_CS_TGSM_RESOURCE_REGISTER_READ_PORTS = 1;
const UINT D3D12_CS_THREADGROUPID_REGISTER_COMPONENTS = 3;
const UINT D3D12_CS_THREADGROUPID_REGISTER_COUNT = 1;
const UINT D3D12_CS_THREADIDINGROUPFLATTENED_REGISTER_COMPONENTS = 1;
const UINT D3D12_CS_THREADIDINGROUPFLATTENED_REGISTER_COUNT = 1;
const UINT D3D12_CS_THREADIDINGROUP_REGISTER_COMPONENTS = 3;
const UINT D3D12_CS_THREADIDINGROUP_REGISTER_COUNT = 1;
const UINT D3D12_CS_THREADID_REGISTER_COMPONENTS = 3;
const UINT D3D12_CS_THREADID_REGISTER_COUNT = 1;
const UINT D3D12_CS_THREAD_GROUP_MAX_THREADS_PER_GROUP = 1024;
const UINT D3D12_CS_THREAD_GROUP_MAX_X = 1024;
const UINT D3D12_CS_THREAD_GROUP_MAX_Y = 1024;
const UINT D3D12_CS_THREAD_GROUP_MAX_Z = 64;
const UINT D3D12_CS_THREAD_GROUP_MIN_X = 1;
const UINT D3D12_CS_THREAD_GROUP_MIN_Y = 1;
const UINT D3D12_CS_THREAD_GROUP_MIN_Z = 1;
const UINT D3D12_CS_THREAD_LOCAL_TEMP_REGISTER_POOL = 16384;
cpp_quote( "#define D3D12_DEFAULT_BLEND_FACTOR_ALPHA	( 1.0f )" )
cpp_quote( "#define D3D12_DEFAULT_BLEND_FACTOR_BLUE	( 1.0f )" )
cpp_quote( "#define D3D12_DEFAULT_BLEND_FACTOR_GREEN	( 1.0f )" )
cpp_quote( "#define D3D12_DEFAULT_BLEND_FACTOR_RED	( 1.0f )" )
cpp_quote( "#define D3D12_DEFAULT_BORDER_COLOR_COMPONENT	( 0.0f )" )
const UINT D3D12_DEFAULT_DEPTH_BIAS = 0;
cpp_quote( "#define D3D12_DEFAULT_DEPTH_BIAS_CLAMP	( 0.0f )" )
const UINT D3D12_DEFAULT_MAX_ANISOTROPY = 16;
cpp_quote( "#define D3D12_DEFAULT_MIP_LOD_BIAS	( 0.0f )" )
const UINT D3D12_DEFAULT_MSAA_RESOURCE_PLACEMENT_ALIGNMENT = 4194304;
const UINT D3D12_DEFAULT_RENDER_TARGET_ARRAY_INDEX = 0;
const UINT D3D12_DEFAULT_RESOURCE_PLACEMENT_ALIGNMENT = 65536;
const UINT D3D12_DEFAULT_SAMPLE_MASK = 0xffffffff;
const UINT D3D12_DEFAULT_SCISSOR_ENDX = 0;
const UINT D3D12_DEFAULT_SCISSOR_ENDY = 0;
const UINT D3D12_DEFAULT_SCISSOR_STARTX = 0;
const UINT D3D12_DEFAULT_SCISSOR_STARTY = 0;
cpp_quote( "#define D3D12_DEFAULT_SLOPE_SCALED_DEPTH_BIAS	( 0.0f )" )
const UINT D3D12_DEFAULT_STENCIL_READ_MASK = 0xff;
const UINT D3D12_DEFAULT_STENCIL_REFERENCE = 0;
const UINT D3D12_DEFAULT_STENCIL_WRITE_MASK = 0xff;
const UINT D3D12_DEFAULT_VIEWPORT_AND_SCISSORRECT_INDEX = 0;
const UINT D3D12_DEFAULT_VIEWPORT_HEIGHT = 0;
cpp_quote( "#define D3D12_DEFAULT_VIEWPORT_MAX_DEPTH	( 0.0f )" )
cpp_quote( "#define D3D12_DEFAULT_VIEWPORT_MIN_DEPTH	( 0.0f )" )
const UINT D3D12_DEFAULT_VIEWPORT_TOPLEFTX = 0;
const UINT D3D12_DEFAULT_VIEWPORT_TOPLEFTY = 0;
const UINT D3D12_DEFAULT_VIEWPORT_WIDTH = 0;
const UINT D3D12_DESCRIPTOR_RANGE_OFFSET_APPEND = 0xffffffff;
const UINT D3D12_DRIVER_RESERVED_REGISTER_SPACE_VALUES_END = 0xfffffff7;
const UINT D3D12_DRIVER_RESERVED_REGISTER_SPACE_VALUES_START = 0xfffffff0;
const UINT D3D12_DS_INPUT_CONTROL_POINTS_MAX_TOTAL_SCALARS = 3968;
const UINT D3D12_DS_INPUT_CONTROL_POINT_REGISTER_COMPONENTS = 4;
const UINT D3D12_DS_INPUT_CONTROL_POINT_REGISTER_COMPONENT_BIT_COUNT = 32;
const UINT D3D12_DS_INPUT_CONTROL_POINT_REGISTER_COUNT = 32;
const UINT D3D12_DS_INPUT_CONTROL_POINT_REGISTER_READS_PER_INST = 2;
const UINT D3D12_DS_INPUT_CONTROL_POINT_REGISTER_READ_PORTS = 1;
const UINT D3D12_DS_INPUT_DOMAIN_POINT_REGISTER_COMPONENTS = 3;
const UINT D3D12_DS_INPUT_DOMAIN_POINT_REGISTER_COMPONENT_BIT_COUNT = 32;
const UINT D3D12_DS_INPUT_DOMAIN_POINT_REGISTER_COUNT = 1;
const UINT D3D12_DS_INPUT_DOMAIN_POINT_REGISTER_READS_PER_INST = 2;
const UINT D3D12_DS_INPUT_DOMAIN_POINT_REGISTER_READ_PORTS = 1;
const UINT D3D12_DS_INPUT_PATCH_CONSTANT_REGISTER_COMPONENTS = 4;
const UINT D3D12_DS_INPUT_PATCH_CONSTANT_REGISTER_COMPONENT_BIT_COUNT = 32;
const UINT D3D12_DS_INPUT_PATCH_CONSTANT_REGISTER_COUNT = 32;
const UINT D3D12_DS_INPUT_PATCH_CONSTANT_REGISTER_READS_PER_INST = 2;
const UINT D3D12_DS_INPUT_PATCH_CONSTANT_REGISTER_READ_PORTS = 1;
const UINT D3D12_DS_INPUT_PRIMITIVE_ID_REGISTER_COMPONENTS = 1;
const UINT D3D12_DS_INPUT_PRIMITIVE_ID_REGISTER_COMPONENT_BIT_COUNT = 32;
const UINT D3D12_DS_INPUT_PRIMITIVE_ID_REGISTER_COUNT = 1;
const UINT D3D12_DS_INPUT_PRIMITIVE_ID_REGISTER_READS_PER_INST = 2;
const UINT D3D12_DS_INPUT_PRIMITIVE_ID_REGISTER_READ_PORTS = 1;
const UINT D3D12_DS_OUTPUT_REGISTER_COMPONENTS = 4;
const UINT D3D12_DS_OUTPUT_REGISTER_COMPONENT_BIT_COUNT = 32;
const UINT D3D12_DS_OUTPUT_REGISTER_COUNT = 32;
cpp_quote( "#define D3D12_FLOAT16_FUSED_TOLERANCE_IN_ULP	( 0.6 )" )
cpp_quote( "#define D3D12_FLOAT32_MAX	( 3.402823466e+38f )" )
cpp_quote( "#define D3D12_FLOAT32_TO_INTEGER_TOLERANCE_IN_ULP	( 0.6f )" )
cpp_quote( "#define D3D12_FLOAT_TO_SRGB_EXPONENT_DENOMINATOR	( 2.4f )" )
cpp_quote( "#define D3D12_FLOAT_TO_SRGB_EXPONENT_NUMERATOR	( 1.0f )" )
cpp_quote( "#define D3D12_FLOAT_TO_SRGB_OFFSET	( 0.055f )" )
cpp_quote( "#define D3D12_FLOAT_TO_SRGB_SCALE_1	( 12.92f )" )
cpp_quote( "#define D3D12_FLOAT_TO_SRGB_SCALE_2	( 1.055f )" )
cpp_quote( "#define D3D12_FLOAT_TO_SRGB_THRESHOLD	( 0.0031308f )" )
cpp_quote( "#define D3D12_FTOI_INSTRUCTION_MAX_INPUT	( 2147483647.999f )" )
cpp_quote( "#define D3D12_FTOI_INSTRUCTION_MIN_INPUT	( -2147483648.999f )" )
cpp_quote( "#define D3D12_FTOU_INSTRUCTION_MAX_INPUT	( 4294967295.999f )" )
cpp_quote( "#define D3D12_FTOU_INSTRUCTION_MIN_INPUT	( 0.0f )" )
const UINT D3D12_GS_INPUT_INSTANCE_ID_READS_PER_INST = 2;
const UINT D3D12_GS_INPUT_INSTANCE_ID_READ_PORTS = 1;
const UINT D3D12_GS_INPUT_INSTANCE_ID_REGISTER_COMPONENTS = 1;
const UINT D3D12_GS_INPUT_INSTANCE_ID_REGISTER_COMPONENT_BIT_COUNT = 32;
const UINT D3D12_GS_INPUT_INSTANCE_ID_REGISTER_COUNT = 1;
const UINT D3D12_GS_INPUT_PRIM_CONST_REGISTER_COMPONENTS = 1;
const UINT D3D12_GS_INPUT_PRIM_CONST_REGISTER_COMPONENT_BIT_COUNT = 32;
const UINT D3D12_GS_INPUT_PRIM_CONST_REGISTER_COUNT = 1;
const UINT D3D12_GS_INPUT_PRIM_CONST_REGISTER_READS_PER_INST = 2;
const UINT D3D12_GS_INPUT_PRIM_CONST_REGISTER_READ_PORTS = 1;
const UINT D3D12_GS_INPUT_REGISTER_COMPONENTS = 4;
const UINT D3D12_GS_INPUT_REGISTER_COMPONENT_BIT_COUNT = 32;
const UINT D3D12_GS_INPUT_REGISTER_COUNT = 32;
const UINT D3D12_GS_INPUT_REGISTER_READS_PER_INST = 2;
const UINT D3D12_GS_INPUT_REGISTER_READ_PORTS = 1;
const UINT D3D12_GS_INPUT_REGISTER_VERTICES = 32;
const UINT D3D12_GS_MAX_INSTANCE_COUNT = 32;
const UINT D3D12_GS_MAX_OUTPUT_VERTEX_COUNT_ACROSS_INSTANCES = 1024;
const UINT D3D12_GS_OUTPUT_ELEMENTS = 32;
const UINT D3D12_GS_OUTPUT_REGISTER_COMPONENTS = 4;
const UINT D3D12_GS_OUTPUT_REGISTER_COMPONENT_BIT_COUNT = 32;
const UINT D3D12_GS_OUTPUT_REGISTER_COUNT = 32;
const UINT D3D12_HS_CONTROL_POINT_PHASE_INPUT_REGISTER_COUNT = 32;
const UINT D3D12_HS_CONTROL_POINT_PHASE_OUTPUT_REGISTER_COUNT = 32;
const UINT D3D12_HS_CONTROL_POINT_REGISTER_COMPONENTS = 4;
const UINT D3D12_HS_CONTROL_POINT_REGISTER_COMPONENT_BIT_COUNT = 32;
const UINT D3D12_HS_CONTROL_POINT_REGISTER_READS_PER_INST = 2;
const UINT D3D12_HS_CONTROL_POINT_REGISTER_READ_PORTS = 1;
const UINT D3D12_HS_FORK_PHASE_INSTANCE_COUNT_UPPER_BOUND = 0xFFFFFFFF;
const UINT D3D12_HS_INPUT_FORK_INSTANCE_ID_REGISTER_COMPONENTS = 1;
const UINT D3D12_HS_INPUT_FORK_INSTANCE_ID_REGISTER_COMPONENT_BIT_COUNT = 32;
const UINT D3D12_HS_INPUT_FORK_INSTANCE_ID_REGISTER_COUNT = 1;
const UINT D3D12_HS_INPUT_FORK_INSTANCE_ID_REGISTER_READS_PER_INST = 2;
const UINT D3D12_HS_INPUT_FORK_INSTANCE_ID_REGISTER_READ_PORTS = 1;
const UINT D3D12_HS_INPUT_JOIN_INSTANCE_ID_REGISTER_COMPONENTS = 1;
const UINT D3D12_HS_INPUT_JOIN_INSTANCE_ID_REGISTER_COMPONENT_BIT_COUNT = 32;
const UINT D3D12_HS_INPUT_JOIN_INSTANCE_ID_REGISTER_COUNT = 1;
const UINT D3D12_HS_INPUT_JOIN_INSTANCE_ID_REGISTER_READS_PER_INST = 2;
const UINT D3D12_HS_INPUT_JOIN_INSTANCE_ID_REGISTER_READ_PORTS = 1;
const UINT D3D12_HS_INPUT_PRIMITIVE_ID_REGISTER_COMPONENTS = 1;
const UINT D3D12_HS_INPUT_PRIMITIVE_ID_REGISTER_COMPONENT_BIT_COUNT = 32;
const UINT D3D12_HS_INPUT_PRIMITIVE_ID_REGISTER_COUNT = 1;
const UINT D3D12_HS_INPUT_PRIMITIVE_ID_REGISTER_READS_PER_INST = 2;
const UINT D3D12_HS_INPUT_PRIMITIVE_ID_REGISTER_READ_PORTS = 1;
const UINT D3D12_HS_JOIN_PHASE_INSTANCE_COUNT_UPPER_BOUND = 0xFFFFFFFF;
cpp_quote( "#define D3D12_HS_MAXTESSFACTOR_LOWER_BOUND	( 1.0f )" )
cpp_quote( "#define D3D12_HS_MAXTESSFACTOR_UPPER_BOUND	( 64.0f )" )
const UINT D3D12_HS_OUTPUT_CONTROL_POINTS_MAX_TOTAL_SCALARS = 3968;
const UINT D3D12_HS_OUTPUT_CONTROL_POINT_ID_REGISTER_COMPONENTS = 1;
const UINT D3D12_HS_OUTPUT_CONTROL_POINT_ID_REGISTER_COMPONENT_BIT_COUNT = 32;
const UINT D3D12_HS_OUTPUT_CONTROL_POINT_ID_REGISTER_COUNT = 1;
const UINT D3D12_HS_OUTPUT_CONTROL_POINT_ID_REGISTER_READS_PER_INST = 2;
const UINT D3D12_HS_OUTPUT_CONTROL_POINT_ID_REGISTER_READ_PORTS = 1;
const UINT D3D12_HS_OUTPUT_PATCH_CONSTANT_REGISTER_COMPONENTS = 4;
const UINT D3D12_HS_OUTPUT_PATCH_CONSTANT_REGISTER_COMPONENT_BIT_COUNT = 32;
const UINT D3D12_HS_OUTPUT_PATCH_CONSTANT_REGISTER_COUNT = 32;
const UINT D3D12_HS_OUTPUT_PATCH_CONSTANT_REGISTER_READS_PER_INST = 2;
const UINT D3D12_HS_OUTPUT_PATCH_CONSTANT_REGISTER_READ_PORTS = 1;
const UINT D3D12_HS_OUTPUT_PATCH_CONSTANT_REGISTER_SCALAR_COMPONENTS = 128;
const UINT D3D12_IA_DEFAULT_INDEX_BUFFER_OFFSET_IN_BYTES = 0;
const UINT D3D12_IA_DEFAULT_PRIMITIVE_TOPOLOGY = 0;
const UINT D3D12_IA_DEFAULT_VERTEX_BUFFER_OFFSET_IN_BYTES = 0;
const UINT D3D12_IA_INDEX_INPUT_RESOURCE_SLOT_COUNT = 1;
const UINT D3D12_IA_INSTANCE_ID_BIT_COUNT = 32;
const UINT D3D12_IA_INTEGER_ARITHMETIC_BIT_COUNT = 32;
const UINT D3D12_IA_PATCH_MAX_CONTROL_POINT_COUNT = 32;
const UINT D3D12_IA_PRIMITIVE_ID_BIT_COUNT = 32;
const UINT D3D12_IA_VERTEX_ID_BIT_COUNT = 32;
const UINT D3D12_IA_VERTEX_INPUT_RESOURCE_SLOT_COUNT = 32;
const UINT D3D12_IA_VERTEX_INPUT_STRUCTURE_ELEMENTS_COMPONENTS = 128;
const UINT D3D12_IA_VERTEX_INPUT_STRUCTURE_ELEMENT_COUNT = 32;
const UINT D3D12_INTEGER_DIVIDE_BY_ZERO_QUOTIENT = 0xffffffff;
const UINT D3D12_INTEGER_DIVIDE_BY_ZERO_REMAINDER = 0xffffffff;
const UINT D3D12_KEEP_RENDER_TARGETS_AND_DEPTH_STENCIL = 0xffffffff;
const UINT D3D12_KEEP_UNORDERED_ACCESS_VIEWS = 0xffffffff;
cpp_quote( "#define D3D12_LINEAR_GAMMA	( 1.0f )" )
const UINT D3D12_MAJOR_VERSION = 12;
cpp_quote( "#define D3D12_MAX_BORDER_COLOR_COMPONENT	( 1.0f )" )
cpp_quote( "#define D3D12_MAX_DEPTH	( 1.0f )" )
const UINT D3D12_MAX_LIVE_STATIC_SAMPLERS = 2032;
const UINT D3D12_MAX_MAXANISOTROPY = 16;
const UINT D3D12_MAX_MULTISAMPLE_SAMPLE_COUNT = 32;
cpp_quote( "#define D3D12_MAX_POSITION_VALUE	( 3.402823466e+34f )" )
const UINT D3D12_MAX_ROOT_COST = 64;
const UINT D3D12_MAX_SHADER_VISIBLE_DESCRIPTOR_HEAP_SIZE_TIER_1 = 1000000;
const UINT D3D12_MAX_SHADER_VISIBLE_DESCRIPTOR_HEAP_SIZE_TIER_2 = 1000000;
const UINT D3D12_MAX_SHADER_VISIBLE_SAMPLER_HEAP_SIZE = 2048;
const UINT D3D12_MAX_TEXTURE_DIMENSION_2_TO_EXP = 17;
const UINT D3D12_MAX_VIEW_INSTANCE_COUNT = 4;
const UINT D3D12_MINOR_VERSION = 0;
cpp_quote( "#define D3D12_MIN_BORDER_COLOR_COMPONENT	( 0.0f )" )
cpp_quote( "#define D3D12_MIN_DEPTH	( 0.0f )" )
const UINT D3D12_MIN_MAXANISOTROPY = 0;
cpp_quote( "#define D3D12_MIP_LOD_BIAS_MAX	( 15.99f )" )
cpp_quote( "#define D3D12_MIP_LOD_BIAS_MIN	( -16.0f )" )
const UINT D3D12_MIP_LOD_FRACTIONAL_BIT_COUNT = 8;
const UINT D3D12_MIP_LOD_RANGE_BIT_COUNT = 8;
cpp_quote( "#define D3D12_MULTISAMPLE_ANTIALIAS_LINE_WIDTH	( 1.4f )" )
const UINT D3D12_NONSAMPLE_FETCH_OUT_OF_RANGE_ACCESS_RESULT = 0;
const UINT D3D12_OS_RESERVED_REGISTER_SPACE_VALUES_END = 0xffffffff;
const UINT D3D12_OS_RESERVED_REGISTER_SPACE_VALUES_START = 0xfffffff8;
const UINT D3D12_PACKED_TILE = 0xffffffff;
const UINT D3D12_PIXEL_ADDRESS_RANGE_BIT_COUNT = 15;
const UINT D3D12_PREVIEW_SDK_VERSION = 714;
const UINT D3D12_PRE_SCISSOR_PIXEL_ADDRESS_RANGE_BIT_COUNT = 16;
const UINT D3D12_PS_CS_UAV_REGISTER_COMPONENTS = 1;
const UINT D3D12_PS_CS_UAV_REGISTER_COUNT = 8;
const UINT D3D12_PS_CS_UAV_REGISTER_READS_PER_INST = 1;
const UINT D3D12_PS_CS_UAV_REGISTER_READ_PORTS = 1;
const UINT D3D12_PS_FRONTFACING_DEFAULT_VALUE = 0xFFFFFFFF;
const UINT D3D12_PS_FRONTFACING_FALSE_VALUE = 0x00000000;
const UINT D3D12_PS_FRONTFACING_TRUE_VALUE = 0xFFFFFFFF;
const UINT D3D12_PS_INPUT_REGISTER_COMPONENTS = 4;
const UINT D3D12_PS_INPUT_REGISTER_COMPONENT_BIT_COUNT = 32;
const UINT D3D12_PS_INPUT_REGISTER_COUNT = 32;
const UINT D3D12_PS_INPUT_REGISTER_READS_PER_INST = 2;
const UINT D3D12_PS_INPUT_REGISTER_READ_PORTS = 1;
cpp_quote( "#define D3D12_PS_LEGACY_PIXEL_CENTER_FRACTIONAL_COMPONENT	( 0.0f )" )
const UINT D3D12_PS_OUTPUT_DEPTH_REGISTER_COMPONENTS = 1;
const UINT D3D12_PS_OUTPUT_DEPTH_REGISTER_COMPONENT_BIT_COUNT = 32;
const UINT D3D12_PS_OUTPUT_DEPTH_REGISTER_COUNT = 1;
const UINT D3D12_PS_OUTPUT_MASK_REGISTER_COMPONENTS = 1;
const UINT D3D12_PS_OUTPUT_MASK_REGISTER_COMPONENT_BIT_COUNT = 32;
const UINT D3D12_PS_OUTPUT_MASK_REGISTER_COUNT = 1;
const UINT D3D12_PS_OUTPUT_REGISTER_COMPONENTS = 4;
const UINT D3D12_PS_OUTPUT_REGISTER_COMPONENT_BIT_COUNT = 32;
const UINT D3D12_PS_OUTPUT_REGISTER_COUNT = 8;
cpp_quote( "#define D3D12_PS_PIXEL_CENTER_FRACTIONAL_COMPONENT	( 0.5f )" )
const UINT D3D12_RAW_UAV_SRV_BYTE_ALIGNMENT = 16;
const UINT D3D12_RAYTRACING_AABB_BYTE_ALIGNMENT = 8;
const UINT D3D12_RAYTRACING_ACCELERATION_STRUCTURE_BYTE_ALIGNMENT = 256;
const UINT D3D12_RAYTRACING_INSTANCE_DESCS_BYTE_ALIGNMENT = 16;
const UINT D3D12_RAYTRACING_MAX_ATTRIBUTE_SIZE_IN_BYTES = 32;
const UINT D3D12_RAYTRACING_MAX_DECLARABLE_TRACE_RECURSION_DEPTH = 31;
const UINT D3D12_RAYTRACING_MAX_GEOMETRIES_PER_BOTTOM_LEVEL_ACCELERATION_STRUCTURE = 16777216;
const UINT D3D12_RAYTRACING_MAX_INSTANCES_PER_TOP_LEVEL_ACCELERATION_STRUCTURE = 16777216;
const UINT D3D12_RAYTRACING_MAX_PRIMITIVES_PER_BOTTOM_LEVEL_ACCELERATION_STRUCTURE = 536870912;
const UINT D3D12_RAYTRACING_MAX_RAY_GENERATION_SHADER_THREADS = 1073741824;
const UINT D3D12_RAYTRACING_MAX_SHADER_RECORD_STRIDE = 4096;
const UINT D3D12_RAYTRACING_SHADER_RECORD_BYTE_ALIGNMENT = 32;
const UINT D3D12_RAYTRACING_SHADER_TABLE_BYTE_ALIGNMENT = 64;
const UINT D3D12_RAYTRACING_TRANSFORM3X4_BYTE_ALIGNMENT = 16;
const UINT D3D12_REQ_BLEND_OBJECT_COUNT_PER_DEVICE = 4096;
const UINT D3D12_REQ_BUFFER_RESOURCE_TEXEL_COUNT_2_TO_EXP = 27;
const UINT D3D12_REQ_CONSTANT_BUFFER_ELEMENT_COUNT = 4096;
const UINT D3D12_REQ_DEPTH_STENCIL_OBJECT_COUNT_PER_DEVICE = 4096;
const UINT D3D12_REQ_DRAWINDEXED_INDEX_COUNT_2_TO_EXP = 32;
const UINT D3D12_REQ_DRAW_VERTEX_COUNT_2_TO_EXP = 32;
const UINT D3D12_REQ_FILTERING_HW_ADDRESSABLE_RESOURCE_DIMENSION = 16384;
const UINT D3D12_REQ_GS_INVOCATION_32BIT_OUTPUT_COMPONENT_LIMIT = 1024;
const UINT D3D12_REQ_IMMEDIATE_CONSTANT_BUFFER_ELEMENT_COUNT = 4096;
const UINT D3D12_REQ_MAXANISOTROPY = 16;
const UINT D3D12_REQ_MIP_LEVELS = 15;
const UINT D3D12_REQ_MULTI_ELEMENT_STRUCTURE_SIZE_IN_BYTES = 2048;
const UINT D3D12_REQ_RASTERIZER_OBJECT_COUNT_PER_DEVICE = 4096;
const UINT D3D12_REQ_RENDER_TO_BUFFER_WINDOW_WIDTH = 16384;
const UINT D3D12_REQ_RESOURCE_SIZE_IN_MEGABYTES_EXPRESSION_A_TERM = 128;
cpp_quote( "#define D3D12_REQ_RESOURCE_SIZE_IN_MEGABYTES_EXPRESSION_B_TERM	( 0.25f )" )
const UINT D3D12_REQ_RESOURCE_SIZE_IN_MEGABYTES_EXPRESSION_C_TERM = 2048;
const UINT D3D12_REQ_RESOURCE_VIEW_COUNT_PER_DEVICE_2_TO_EXP = 20;
const UINT D3D12_REQ_SAMPLER_OBJECT_COUNT_PER_DEVICE = 4096;
const UINT D3D12_REQ_SUBRESOURCES = 30720;
const UINT D3D12_REQ_TEXTURE1D_ARRAY_AXIS_DIMENSION = 2048;
const UINT D3D12_REQ_TEXTURE1D_U_DIMENSION = 16384;
const UINT D3D12_REQ_TEXTURE2D_ARRAY_AXIS_DIMENSION = 2048;
const UINT D3D12_REQ_TEXTURE2D_U_OR_V_DIMENSION = 16384;
const UINT D3D12_REQ_TEXTURE3D_U_V_OR_W_DIMENSION = 2048;
const UINT D3D12_REQ_TEXTURECUBE_DIMENSION = 16384;
const UINT D3D12_RESINFO_INSTRUCTION_MISSING_COMPONENT_RETVAL = 0;
const UINT D3D12_RESOURCE_BARRIER_ALL_SUBRESOURCES = 0xffffffff;
const UINT D3D12_RS_SET_SHADING_RATE_COMBINER_COUNT = 2;
const UINT D3D12_SDK_VERSION = 614;
const UINT D3D12_SHADER_IDENTIFIER_SIZE_IN_BYTES = 32;
const UINT D3D12_SHADER_MAJOR_VERSION = 5;
const UINT D3D12_SHADER_MAX_INSTANCES = 65535;
const UINT D3D12_SHADER_MAX_INTERFACES = 253;
const UINT D3D12_SHADER_MAX_INTERFACE_CALL_SITES = 4096;
const UINT D3D12_SHADER_MAX_TYPES = 65535;
const UINT D3D12_SHADER_MINOR_VERSION = 1;
const UINT D3D12_SHIFT_INSTRUCTION_PAD_VALUE = 0;
const UINT D3D12_SHIFT_INSTRUCTION_SHIFT_VALUE_BIT_COUNT = 5;
const UINT D3D12_SIMULTANEOUS_RENDER_TARGET_COUNT = 8;
const UINT D3D12_SMALL_MSAA_RESOURCE_PLACEMENT_ALIGNMENT = 65536;
const UINT D3D12_SMALL_RESOURCE_PLACEMENT_ALIGNMENT = 4096;
const UINT D3D12_SO_BUFFER_MAX_STRIDE_IN_BYTES = 2048;
const UINT D3D12_SO_BUFFER_MAX_WRITE_WINDOW_IN_BYTES = 512;
const UINT D3D12_SO_BUFFER_SLOT_COUNT = 4;
const UINT D3D12_SO_DDI_REGISTER_INDEX_DENOTING_GAP = 0xffffffff;
const UINT D3D12_SO_NO_RASTERIZED_STREAM = 0xffffffff;
const UINT D3D12_SO_OUTPUT_COMPONENT_COUNT = 128;
const UINT D3D12_SO_STREAM_COUNT = 4;
const UINT D3D12_SPEC_DATE_DAY = 14;
const UINT D3D12_SPEC_DATE_MONTH = 11;
const UINT D3D12_SPEC_DATE_YEAR = 2014;
cpp_quote( "#define D3D12_SPEC_VERSION	( 1.16 )" )
cpp_quote( "#define D3D12_SRGB_GAMMA	( 2.2f )" )
cpp_quote( "#define D3D12_SRGB_TO_FLOAT_DENOMINATOR_1	( 12.92f )" )
cpp_quote( "#define D3D12_SRGB_TO_FLOAT_DENOMINATOR_2	( 1.055f )" )
cpp_quote( "#define D3D12_SRGB_TO_FLOAT_EXPONENT	( 2.4f )" )
cpp_quote( "#define D3D12_SRGB_TO_FLOAT_OFFSET	( 0.055f )" )
cpp_quote( "#define D3D12_SRGB_TO_FLOAT_THRESHOLD	( 0.04045f )" )
cpp_quote( "#define D3D12_SRGB_TO_FLOAT_TOLERANCE_IN_ULP	( 0.5f )" )
const UINT D3D12_STANDARD_COMPONENT_BIT_COUNT = 32;
const UINT D3D12_STANDARD_COMPONENT_BIT_COUNT_DOUBLED = 64;
const UINT D3D12_STANDARD_MAXIMUM_ELEMENT_ALIGNMENT_BYTE_MULTIPLE = 4;
const UINT D3D12_STANDARD_PIXEL_COMPONENT_COUNT = 128;
const UINT D3D12_STANDARD_PIXEL_ELEMENT_COUNT = 32;
const UINT D3D12_STANDARD_VECTOR_SIZE = 4;
const UINT D3D12_STANDARD_VERTEX_ELEMENT_COUNT = 32;
const UINT D3D12_STANDARD_VERTEX_TOTAL_COMPONENT_COUNT = 64;
const UINT D3D12_SUBPIXEL_FRACTIONAL_BIT_COUNT = 8;
const UINT D3D12_SUBTEXEL_FRACTIONAL_BIT_COUNT = 8;
const UINT D3D12_SYSTEM_RESERVED_REGISTER_SPACE_VALUES_END = 0xffffffff;
const UINT D3D12_SYSTEM_RESERVED_REGISTER_SPACE_VALUES_START = 0xfffffff0;
const UINT D3D12_TESSELLATOR_MAX_EVEN_TESSELLATION_FACTOR = 64;
const UINT D3D12_TESSELLATOR_MAX_ISOLINE_DENSITY_TESSELLATION_FACTOR = 64;
const UINT D3D12_TESSELLATOR_MAX_ODD_TESSELLATION_FACTOR = 63;
const UINT D3D12_TESSELLATOR_MAX_TESSELLATION_FACTOR = 64;
const UINT D3D12_TESSELLATOR_MIN_EVEN_TESSELLATION_FACTOR = 2;
const UINT D3D12_TESSELLATOR_MIN_ISOLINE_DENSITY_TESSELLATION_FACTOR = 1;
const UINT D3D12_TESSELLATOR_MIN_ODD_TESSELLATION_FACTOR = 1;
const UINT D3D12_TEXEL_ADDRESS_RANGE_BIT_COUNT = 16;
const UINT D3D12_TEXTURE_DATA_PITCH_ALIGNMENT = 256;
const UINT D3D12_TEXTURE_DATA_PLACEMENT_ALIGNMENT = 512;
const UINT D3D12_TILED_RESOURCE_TILE_SIZE_IN_BYTES = 65536;
const UINT D3D12_TRACKED_WORKLOAD_MAX_INSTANCES = 32;
const UINT D3D12_UAV_COUNTER_PLACEMENT_ALIGNMENT = 4096;
const UINT D3D12_UAV_SLOT_COUNT = 64;
const UINT D3D12_UNBOUND_MEMORY_ACCESS_RESULT = 0;
const UINT D3D12_VIDEO_DECODE_MAX_ARGUMENTS = 10;
const UINT D3D12_VIDEO_DECODE_MAX_HISTOGRAM_COMPONENTS = 4;
const UINT D3D12_VIDEO_DECODE_MIN_BITSTREAM_OFFSET_ALIGNMENT = 256;
const UINT D3D12_VIDEO_DECODE_MIN_HISTOGRAM_OFFSET_ALIGNMENT = 256;
const UINT D3D12_VIDEO_DECODE_STATUS_MACROBLOCKS_AFFECTED_UNKNOWN = 0xffffffff;
const UINT D3D12_VIDEO_ENCODER_AV1_INVALID_DPB_RESOURCE_INDEX = 0xFF;
const UINT D3D12_VIDEO_ENCODER_AV1_MAX_TILE_COLS = 64;
const UINT D3D12_VIDEO_ENCODER_AV1_MAX_TILE_ROWS = 64;
const UINT D3D12_VIDEO_ENCODER_AV1_SUPERRES_DENOM_MIN = 9;
const UINT D3D12_VIDEO_ENCODER_AV1_SUPERRES_NUM = 8;
const UINT D3D12_VIDEO_PROCESS_MAX_FILTERS = 32;
const UINT D3D12_VIDEO_PROCESS_STEREO_VIEWS = 2;
const UINT D3D12_VIEWPORT_AND_SCISSORRECT_MAX_INDEX = 15;
const UINT D3D12_VIEWPORT_AND_SCISSORRECT_OBJECT_COUNT_PER_PIPELINE = 16;
const UINT D3D12_VIEWPORT_BOUNDS_MAX = 32767;
const INT D3D12_VIEWPORT_BOUNDS_MIN = -32768;
const UINT D3D12_VS_INPUT_REGISTER_COMPONENTS = 4;
const UINT D3D12_VS_INPUT_REGISTER_COMPONENT_BIT_COUNT = 32;
const UINT D3D12_VS_INPUT_REGISTER_COUNT = 32;
const UINT D3D12_VS_INPUT_REGISTER_READS_PER_INST = 2;
const UINT D3D12_VS_INPUT_REGISTER_READ_PORTS = 1;
const UINT D3D12_VS_OUTPUT_REGISTER_COMPONENTS = 4;
const UINT D3D12_VS_OUTPUT_REGISTER_COMPONENT_BIT_COUNT = 32;
const UINT D3D12_VS_OUTPUT_REGISTER_COUNT = 32;
const UINT D3D12_WHQL_CONTEXT_COUNT_FOR_RESOURCE_LIMIT = 10;
const UINT D3D12_WHQL_DRAWINDEXED_INDEX_COUNT_2_TO_EXP = 25;
const UINT D3D12_WHQL_DRAW_VERTEX_COUNT_2_TO_EXP = 25;
const UINT D3D12_WORK_GRAPHS_BACKING_MEMORY_ALIGNMENT_IN_BYTES = 8;
const UINT D3D12_WORK_GRAPHS_MAX_NODE_DEPTH = 32;
cpp_quote( "#endif" )


// Forward declarations
interface ID3D12CommandQueue;


typedef UINT64 D3D12_GPU_VIRTUAL_ADDRESS;

typedef enum D3D12_COMMAND_LIST_TYPE
{
    D3D12_COMMAND_LIST_TYPE_DIRECT          = 0,
    D3D12_COMMAND_LIST_TYPE_BUNDLE          = 1,
    D3D12_COMMAND_LIST_TYPE_COMPUTE         = 2,
    D3D12_COMMAND_LIST_TYPE_COPY            = 3,
    D3D12_COMMAND_LIST_TYPE_VIDEO_DECODE    = 4,
    D3D12_COMMAND_LIST_TYPE_VIDEO_PROCESS   = 5,
    D3D12_COMMAND_LIST_TYPE_VIDEO_ENCODE    = 6,

    D3D12_COMMAND_LIST_TYPE_NONE            = -1,

} D3D12_COMMAND_LIST_TYPE;

typedef enum D3D12_COMMAND_QUEUE_FLAGS
{
    D3D12_COMMAND_QUEUE_FLAG_NONE = 0x0,
    D3D12_COMMAND_QUEUE_FLAG_DISABLE_GPU_TIMEOUT = 0x1,
} D3D12_COMMAND_QUEUE_FLAGS;
cpp_quote( "DEFINE_ENUM_FLAG_OPERATORS( D3D12_COMMAND_QUEUE_FLAGS )" )

typedef enum D3D12_COMMAND_QUEUE_PRIORITY
{
    D3D12_COMMAND_QUEUE_PRIORITY_NORMAL          = 0,
    D3D12_COMMAND_QUEUE_PRIORITY_HIGH            = 100,
    D3D12_COMMAND_QUEUE_PRIORITY_GLOBAL_REALTIME = 10000
} D3D12_COMMAND_QUEUE_PRIORITY;

typedef struct D3D12_COMMAND_QUEUE_DESC
{
    D3D12_COMMAND_LIST_TYPE Type;
    INT Priority;
    D3D12_COMMAND_QUEUE_FLAGS Flags;
    UINT NodeMask;
} D3D12_COMMAND_QUEUE_DESC;

typedef enum D3D12_PRIMITIVE_TOPOLOGY_TYPE
{
    D3D12_PRIMITIVE_TOPOLOGY_TYPE_UNDEFINED = 0,
    D3D12_PRIMITIVE_TOPOLOGY_TYPE_POINT     = 1,
    D3D12_PRIMITIVE_TOPOLOGY_TYPE_LINE      = 2,
    D3D12_PRIMITIVE_TOPOLOGY_TYPE_TRIANGLE  = 3,
    D3D12_PRIMITIVE_TOPOLOGY_TYPE_PATCH     = 4
} D3D12_PRIMITIVE_TOPOLOGY_TYPE;

typedef enum D3D12_INPUT_CLASSIFICATION
{
    D3D12_INPUT_CLASSIFICATION_PER_VERTEX_DATA = 0,
    D3D12_INPUT_CLASSIFICATION_PER_INSTANCE_DATA = 1
} D3D12_INPUT_CLASSIFICATION;

typedef struct D3D12_INPUT_ELEMENT_DESC
{
    LPCSTR SemanticName;
    UINT SemanticIndex;
    DXGI_FORMAT Format;
    UINT InputSlot;
    UINT AlignedByteOffset;
    D3D12_INPUT_CLASSIFICATION InputSlotClass;
    UINT InstanceDataStepRate;
} D3D12_INPUT_ELEMENT_DESC;

// Keep FILL_MODE values in sync with earlier DX versions (HW consumes values directly).
typedef enum D3D12_FILL_MODE
{
    // 1 was POINT in D3D, unused in D3D12
    D3D12_FILL_MODE_WIREFRAME = 2,
    D3D12_FILL_MODE_SOLID = 3
} D3D12_FILL_MODE;

typedef D3D_PRIMITIVE_TOPOLOGY D3D12_PRIMITIVE_TOPOLOGY;

typedef D3D_PRIMITIVE D3D12_PRIMITIVE;

// Keep CULL_MODE values in sync with earlier DX versions (HW consumes values directly).
typedef enum D3D12_CULL_MODE
{
    D3D12_CULL_MODE_NONE = 1,
    D3D12_CULL_MODE_FRONT = 2,
    D3D12_CULL_MODE_BACK = 3
} D3D12_CULL_MODE;

typedef struct D3D12_SO_DECLARATION_ENTRY
{
    UINT Stream;
    LPCSTR SemanticName;
    UINT SemanticIndex;
    BYTE StartComponent;
    BYTE ComponentCount;
    BYTE OutputSlot;
} D3D12_SO_DECLARATION_ENTRY;

typedef struct D3D12_VIEWPORT
{
    FLOAT TopLeftX;
    FLOAT TopLeftY;
    FLOAT Width;
    FLOAT Height;
    FLOAT MinDepth;
    FLOAT MaxDepth;
} D3D12_VIEWPORT;


typedef RECT D3D12_RECT;


typedef struct D3D12_BOX
{
    UINT left;
    UINT top;
    UINT front;
    UINT right;
    UINT bottom;
    UINT back;
} D3D12_BOX;


////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//
// Depth-Stencil State
//
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

// Keep COMPARISON_FUNC values in sync with earlier DX versions (HW consumes values directly).
typedef enum D3D12_COMPARISON_FUNC
{
    D3D12_COMPARISON_FUNC_NONE = 0,
    D3D12_COMPARISON_FUNC_NEVER = 1,
    D3D12_COMPARISON_FUNC_LESS = 2,
    D3D12_COMPARISON_FUNC_EQUAL = 3,
    D3D12_COMPARISON_FUNC_LESS_EQUAL = 4,
    D3D12_COMPARISON_FUNC_GREATER = 5,
    D3D12_COMPARISON_FUNC_NOT_EQUAL = 6,
    D3D12_COMPARISON_FUNC_GREATER_EQUAL = 7,
    D3D12_COMPARISON_FUNC_ALWAYS = 8
} D3D12_COMPARISON_FUNC;

typedef enum D3D12_DEPTH_WRITE_MASK
{
    D3D12_DEPTH_WRITE_MASK_ZERO = 0,
    D3D12_DEPTH_WRITE_MASK_ALL = 1
} D3D12_DEPTH_WRITE_MASK;

// Keep STENCILOP values in sync with earlier DX versions (HW consumes values directly).
typedef enum D3D12_STENCIL_OP
{
    D3D12_STENCIL_OP_KEEP = 1,
    D3D12_STENCIL_OP_ZERO = 2,
    D3D12_STENCIL_OP_REPLACE = 3,
    D3D12_STENCIL_OP_INCR_SAT = 4,
    D3D12_STENCIL_OP_DECR_SAT = 5,
    D3D12_STENCIL_OP_INVERT = 6,
    D3D12_STENCIL_OP_INCR = 7,
    D3D12_STENCIL_OP_DECR = 8
} D3D12_STENCIL_OP;

typedef struct D3D12_DEPTH_STENCILOP_DESC
{
    D3D12_STENCIL_OP StencilFailOp;
    D3D12_STENCIL_OP StencilDepthFailOp;
    D3D12_STENCIL_OP StencilPassOp;
    D3D12_COMPARISON_FUNC StencilFunc;
} D3D12_DEPTH_STENCILOP_DESC;

typedef struct D3D12_DEPTH_STENCIL_DESC
{
    BOOL DepthEnable;
    D3D12_DEPTH_WRITE_MASK DepthWriteMask;
    D3D12_COMPARISON_FUNC DepthFunc;
    BOOL StencilEnable;
    UINT8 StencilReadMask;
    UINT8 StencilWriteMask;
    D3D12_DEPTH_STENCILOP_DESC FrontFace;
    D3D12_DEPTH_STENCILOP_DESC BackFace;
} D3D12_DEPTH_STENCIL_DESC;

typedef struct D3D12_DEPTH_STENCIL_DESC1
{
    BOOL DepthEnable;
    D3D12_DEPTH_WRITE_MASK DepthWriteMask;
    D3D12_COMPARISON_FUNC DepthFunc;
    BOOL StencilEnable;
    UINT8 StencilReadMask;
    UINT8 StencilWriteMask;
    D3D12_DEPTH_STENCILOP_DESC FrontFace;
    D3D12_DEPTH_STENCILOP_DESC BackFace;
    BOOL DepthBoundsTestEnable;
} D3D12_DEPTH_STENCIL_DESC1;

// Front/back face independent stencil masks
typedef struct D3D12_DEPTH_STENCILOP_DESC1
{
    D3D12_STENCIL_OP StencilFailOp;
    D3D12_STENCIL_OP StencilDepthFailOp;
    D3D12_STENCIL_OP StencilPassOp;
    D3D12_COMPARISON_FUNC StencilFunc;
    UINT8 StencilReadMask;
    UINT8 StencilWriteMask;
} D3D12_DEPTH_STENCILOP_DESC1;

typedef struct D3D12_DEPTH_STENCIL_DESC2
{
    BOOL DepthEnable;
    D3D12_DEPTH_WRITE_MASK DepthWriteMask;
    D3D12_COMPARISON_FUNC DepthFunc;
    BOOL StencilEnable;
    D3D12_DEPTH_STENCILOP_DESC1 FrontFace;
    D3D12_DEPTH_STENCILOP_DESC1 BackFace;
    BOOL DepthBoundsTestEnable;
} D3D12_DEPTH_STENCIL_DESC2;


////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//
// Blend State
//
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

// Keep BLEND values in sync with earlier DX versions (HW consumes values directly).
typedef enum D3D12_BLEND
{
    D3D12_BLEND_ZERO = 1,
    D3D12_BLEND_ONE = 2,
    D3D12_BLEND_SRC_COLOR = 3, // PS output oN.rgb (N is current RT being blended)
    D3D12_BLEND_INV_SRC_COLOR = 4, // 1.0f - PS output oN.rgb
    D3D12_BLEND_SRC_ALPHA = 5, // PS output oN.a
    D3D12_BLEND_INV_SRC_ALPHA = 6, // 1.0f - PS output oN.a
    D3D12_BLEND_DEST_ALPHA = 7, // RT(N).a (N is current RT being blended)
    D3D12_BLEND_INV_DEST_ALPHA = 8, // 1.0f - RT(N).a
    D3D12_BLEND_DEST_COLOR = 9, // RT(N).rgb
    D3D12_BLEND_INV_DEST_COLOR = 10,// 1.0f - RT(N).rgb
    D3D12_BLEND_SRC_ALPHA_SAT = 11,// (f,f,f,1), f = min(1 - RT(N).a, oN.a)
    // 12 reserved (was BOTHSRCALPHA)
    // 13 reserved (was BOTHSRCALPHA)
    D3D12_BLEND_BLEND_FACTOR = 14,
    D3D12_BLEND_INV_BLEND_FACTOR = 15,
    D3D12_BLEND_SRC1_COLOR = 16, // PS output o1.rgb
    D3D12_BLEND_INV_SRC1_COLOR = 17, // 1.0f - PS output o1.rgb
    D3D12_BLEND_SRC1_ALPHA = 18, // PS output o1.a
    D3D12_BLEND_INV_SRC1_ALPHA = 19, // 1.0f - PS output o1.a
    D3D12_BLEND_ALPHA_FACTOR = 20,
    D3D12_BLEND_INV_ALPHA_FACTOR = 21
} D3D12_BLEND;

// Keep BLENDOP values in sync with earlier DX versions (HW consumes values directly).
typedef enum D3D12_BLEND_OP
{
    D3D12_BLEND_OP_ADD = 1,
    D3D12_BLEND_OP_SUBTRACT = 2,
    D3D12_BLEND_OP_REV_SUBTRACT = 3,
    D3D12_BLEND_OP_MIN = 4, // min semantics are like min shader instruction
    D3D12_BLEND_OP_MAX = 5, // max semantics are like max shader instruction
} D3D12_BLEND_OP;

typedef enum D3D12_COLOR_WRITE_ENABLE
{
    D3D12_COLOR_WRITE_ENABLE_RED = 1,
    D3D12_COLOR_WRITE_ENABLE_GREEN = 2,
    D3D12_COLOR_WRITE_ENABLE_BLUE = 4,
    D3D12_COLOR_WRITE_ENABLE_ALPHA = 8,
    D3D12_COLOR_WRITE_ENABLE_ALL = (D3D12_COLOR_WRITE_ENABLE_RED|D3D12_COLOR_WRITE_ENABLE_GREEN|
        D3D12_COLOR_WRITE_ENABLE_BLUE|D3D12_COLOR_WRITE_ENABLE_ALPHA),
} D3D12_COLOR_WRITE_ENABLE;

typedef enum D3D12_LOGIC_OP
{
                                   // Operation: 
                                   // (s == PS output, d = RTV contents)
    D3D12_LOGIC_OP_CLEAR = 0,      // 0
    D3D12_LOGIC_OP_SET,            // 1
    D3D12_LOGIC_OP_COPY,           // s
    D3D12_LOGIC_OP_COPY_INVERTED,  // ~s
    D3D12_LOGIC_OP_NOOP,           // d
    D3D12_LOGIC_OP_INVERT,         // ~d
    D3D12_LOGIC_OP_AND,            // s & d
    D3D12_LOGIC_OP_NAND,           // ~(s & d)
    D3D12_LOGIC_OP_OR,             // s | d
    D3D12_LOGIC_OP_NOR,            // ~(s | d)
    D3D12_LOGIC_OP_XOR,            // s ^ d
    D3D12_LOGIC_OP_EQUIV,          // ~(s ^ d)
    D3D12_LOGIC_OP_AND_REVERSE,    // s & ~d
    D3D12_LOGIC_OP_AND_INVERTED,   // ~s & d
    D3D12_LOGIC_OP_OR_REVERSE,     // s | ~d
    D3D12_LOGIC_OP_OR_INVERTED,    // ~s | d
} D3D12_LOGIC_OP;

typedef struct D3D12_RENDER_TARGET_BLEND_DESC
{
    BOOL BlendEnable;
    BOOL LogicOpEnable; // LogicOpEnable and BlendEnable can't both be true
    D3D12_BLEND SrcBlend;
    D3D12_BLEND DestBlend;
    D3D12_BLEND_OP BlendOp;
    D3D12_BLEND SrcBlendAlpha;
    D3D12_BLEND DestBlendAlpha;
    D3D12_BLEND_OP BlendOpAlpha;
    D3D12_LOGIC_OP LogicOp; // applies to RGBA
    UINT8 RenderTargetWriteMask; // D3D12_COLOR_WRITE_ENABLE
} D3D12_RENDER_TARGET_BLEND_DESC;

typedef struct D3D12_BLEND_DESC
{
    BOOL AlphaToCoverageEnable; // relevant to multisample antialiasing only
    BOOL IndependentBlendEnable; // if FALSE, then replicate the first entry in RenderTarget array to other entries
    D3D12_RENDER_TARGET_BLEND_DESC RenderTarget[D3D12_SIMULTANEOUS_RENDER_TARGET_COUNT];
} D3D12_BLEND_DESC;

cpp_quote( "/* Note, the array size for RenderTarget[] above is D3D12_SIMULTANEOUS_RENDERTARGET_COUNT. ")
cpp_quote( "   IDL processing/generation of this header replaces the define; this comment is merely explaining what happened. */" ) 


////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//
// Rasterizer State
//
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

typedef enum D3D12_CONSERVATIVE_RASTERIZATION_MODE
{
    D3D12_CONSERVATIVE_RASTERIZATION_MODE_OFF       = 0, // Default
    D3D12_CONSERVATIVE_RASTERIZATION_MODE_ON        = 1,
} D3D12_CONSERVATIVE_RASTERIZATION_MODE;

typedef struct D3D12_RASTERIZER_DESC
{
    D3D12_FILL_MODE FillMode;
    D3D12_CULL_MODE CullMode;
    BOOL FrontCounterClockwise;
    INT DepthBias;
    FLOAT DepthBiasClamp;
    FLOAT SlopeScaledDepthBias;
    BOOL DepthClipEnable;
    BOOL MultisampleEnable;
    BOOL AntialiasedLineEnable;
    UINT ForcedSampleCount;  
    D3D12_CONSERVATIVE_RASTERIZATION_MODE ConservativeRaster;
} D3D12_RASTERIZER_DESC;

typedef struct D3D12_RASTERIZER_DESC1
{
    D3D12_FILL_MODE FillMode;
    D3D12_CULL_MODE CullMode;
    BOOL FrontCounterClockwise;
    FLOAT DepthBias;
    FLOAT DepthBiasClamp;
    FLOAT SlopeScaledDepthBias;
    BOOL DepthClipEnable;
    BOOL MultisampleEnable;
    BOOL AntialiasedLineEnable;
    UINT ForcedSampleCount;
    D3D12_CONSERVATIVE_RASTERIZATION_MODE ConservativeRaster;
} D3D12_RASTERIZER_DESC1;

typedef enum D3D12_LINE_RASTERIZATION_MODE
{
    D3D12_LINE_RASTERIZATION_MODE_ALIASED,
    D3D12_LINE_RASTERIZATION_MODE_ALPHA_ANTIALIASED,
    D3D12_LINE_RASTERIZATION_MODE_QUADRILATERAL_WIDE,
    D3D12_LINE_RASTERIZATION_MODE_QUADRILATERAL_NARROW,
} D3D12_LINE_RASTERIZATION_MODE;

typedef struct D3D12_RASTERIZER_DESC2
{
    D3D12_FILL_MODE FillMode;
    D3D12_CULL_MODE CullMode;
    BOOL FrontCounterClockwise;
    FLOAT DepthBias;
    FLOAT DepthBiasClamp;
    FLOAT SlopeScaledDepthBias;
    BOOL DepthClipEnable;
    D3D12_LINE_RASTERIZATION_MODE LineRasterizationMode;
    UINT ForcedSampleCount;
    D3D12_CONSERVATIVE_RASTERIZATION_MODE ConservativeRaster;
} D3D12_RASTERIZER_DESC2;

////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//
// Pipeline State v1
//
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

[ uuid( c54a6b66-72df-4ee8-8be5-a946a1429214 ), object, local, pointer_default( unique ) ]
interface ID3D12RootSignature
    : ID3D12DeviceChild
{
}

typedef struct D3D12_SHADER_BYTECODE
{
    [annotation("_Field_size_bytes_full_(BytecodeLength)")] const void* pShaderBytecode;
    SIZE_T BytecodeLength;
} D3D12_SHADER_BYTECODE;

typedef struct D3D12_STREAM_OUTPUT_DESC
{
    [annotation("_Field_size_full_(NumEntries)")] const D3D12_SO_DECLARATION_ENTRY* pSODeclaration;
    UINT NumEntries;
    [annotation("_Field_size_full_(NumStrides)")] const UINT* pBufferStrides;
    UINT NumStrides;
    UINT RasterizedStream;
} D3D12_STREAM_OUTPUT_DESC;

typedef struct D3D12_INPUT_LAYOUT_DESC
{
    [annotation("_Field_size_full_(NumElements)")] const D3D12_INPUT_ELEMENT_DESC* pInputElementDescs;
    UINT NumElements;
} D3D12_INPUT_LAYOUT_DESC;

typedef enum D3D12_INDEX_BUFFER_STRIP_CUT_VALUE
{
    D3D12_INDEX_BUFFER_STRIP_CUT_VALUE_DISABLED = 0,
    D3D12_INDEX_BUFFER_STRIP_CUT_VALUE_0xFFFF = 1,
    D3D12_INDEX_BUFFER_STRIP_CUT_VALUE_0xFFFFFFFF = 2
} D3D12_INDEX_BUFFER_STRIP_CUT_VALUE;

typedef enum D3D12_STANDARD_MULTISAMPLE_QUALITY_LEVELS
{
    D3D12_STANDARD_MULTISAMPLE_PATTERN = 0xffffffff,
    D3D12_CENTER_MULTISAMPLE_PATTERN = 0xfffffffe
} 	D3D12_STANDARD_MULTISAMPLE_QUALITY_LEVELS;

typedef struct D3D12_CACHED_PIPELINE_STATE
{
    [annotation("_Field_size_bytes_full_(CachedBlobSizeInBytes)")] const void* pCachedBlob;
    SIZE_T CachedBlobSizeInBytes;
} D3D12_CACHED_PIPELINE_STATE;

typedef enum D3D12_PIPELINE_STATE_FLAGS
{
    D3D12_PIPELINE_STATE_FLAG_NONE = 0x0,
    D3D12_PIPELINE_STATE_FLAG_TOOL_DEBUG = 0x1,
    D3D12_PIPELINE_STATE_FLAG_DYNAMIC_DEPTH_BIAS = 0x4,
    D3D12_PIPELINE_STATE_FLAG_DYNAMIC_INDEX_BUFFER_STRIP_CUT = 0x8,

} D3D12_PIPELINE_STATE_FLAGS;
cpp_quote( "DEFINE_ENUM_FLAG_OPERATORS( D3D12_PIPELINE_STATE_FLAGS )" )

typedef struct D3D12_GRAPHICS_PIPELINE_STATE_DESC
{
    ID3D12RootSignature*          pRootSignature;
    D3D12_SHADER_BYTECODE         VS;
    D3D12_SHADER_BYTECODE         PS;
    D3D12_SHADER_BYTECODE         DS;
    D3D12_SHADER_BYTECODE         HS;
    D3D12_SHADER_BYTECODE         GS;
    D3D12_STREAM_OUTPUT_DESC      StreamOutput;
    D3D12_BLEND_DESC              BlendState;
    UINT                          SampleMask;
    D3D12_RASTERIZER_DESC         RasterizerState;
    D3D12_DEPTH_STENCIL_DESC      DepthStencilState;
    D3D12_INPUT_LAYOUT_DESC       InputLayout;
    D3D12_INDEX_BUFFER_STRIP_CUT_VALUE IBStripCutValue;
    D3D12_PRIMITIVE_TOPOLOGY_TYPE PrimitiveTopologyType;
    UINT                          NumRenderTargets;
    DXGI_FORMAT                   RTVFormats[ D3D12_SIMULTANEOUS_RENDER_TARGET_COUNT ];
    DXGI_FORMAT                   DSVFormat;
    DXGI_SAMPLE_DESC              SampleDesc;
    UINT                          NodeMask;
    D3D12_CACHED_PIPELINE_STATE   CachedPSO;
    D3D12_PIPELINE_STATE_FLAGS    Flags;
} D3D12_GRAPHICS_PIPELINE_STATE_DESC;

typedef struct D3D12_COMPUTE_PIPELINE_STATE_DESC
{
    ID3D12RootSignature*   pRootSignature;
    D3D12_SHADER_BYTECODE  CS;
    UINT                   NodeMask;
    D3D12_CACHED_PIPELINE_STATE   CachedPSO;
    D3D12_PIPELINE_STATE_FLAGS    Flags;
} D3D12_COMPUTE_PIPELINE_STATE_DESC;

////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//
// Pipeline State v2
//
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

struct D3D12_RT_FORMAT_ARRAY
{
    DXGI_FORMAT RTFormats[D3D12_SIMULTANEOUS_RENDER_TARGET_COUNT];
    UINT NumRenderTargets;
};

typedef struct D3D12_PIPELINE_STATE_STREAM_DESC
{
    [annotation("_In_")] SIZE_T SizeInBytes;
    [annotation("_In_reads_(_Inexpressible_(\"Dependent on size of subobjects\"))")] void* pPipelineStateSubobjectStream;
} D3D12_PIPELINE_STATE_STREAM_DESC;

typedef enum D3D12_PIPELINE_STATE_SUBOBJECT_TYPE
{
    D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_ROOT_SIGNATURE, // ID3D12RootSignature*
    D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_VS, // D3D12_SHADER_BYTECODE
    D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_PS, // D3D12_SHADER_BYTECODE
    D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_DS, // D3D12_SHADER_BYTECODE
    D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_HS, // D3D12_SHADER_BYTECODE
    D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_GS, // D3D12_SHADER_BYTECODE
    D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_CS, // D3D12_SHADER_BYTECODE
    D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_STREAM_OUTPUT, // D3D12_STREAM_OUTPUT_DESC
    D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_BLEND, // D3D12_BLEND_DESC
    D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_SAMPLE_MASK, // UINT
    D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_RASTERIZER, // D3D12_RASTERIZER_DESC
    D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_DEPTH_STENCIL, // D3D12_DEPTH_STENCIL_DESC
    D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_INPUT_LAYOUT, // D3D12_INPUT_LAYOUT_DESC
    D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_IB_STRIP_CUT_VALUE, // D3D12_INDEX_BUFFER_STRIP_CUT_VALUE
    D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_PRIMITIVE_TOPOLOGY, // D3D12_PRIMITIVE_TOPOLOGY_TYPE
    D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_RENDER_TARGET_FORMATS, // D3D12_RT_FORMAT_ARRAY
    D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_DEPTH_STENCIL_FORMAT, // DXGI_FORMAT
    D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_SAMPLE_DESC, // DXGI_SAMPLE_DESC
    D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_NODE_MASK, // UINT
    D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_CACHED_PSO, // D3D12_CACHED_PIPELINE_STATE
    D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_FLAGS, // D3D12_PIPELINE_STATE_FLAGS
    D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_DEPTH_STENCIL1, // D3D12_DEPTH_STENCIL_DESC1
    D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_VIEW_INSTANCING, // D3D12_VIEW_INSTANCING_DESC
    D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_AS = 24, // D3D12_SHADER_BYTECODE
    D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_MS = 25, // D3D12_SHADER_BYTECODE
    D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_DEPTH_STENCIL2 = 26, // D3D12_DEPTH_STENCIL_DESC2
    D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_RASTERIZER1 = 27, // D3D12_RASTERIZER_DESC1
    D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_RASTERIZER2 = 28, // D3D12_RASTERIZER_DESC2
    D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_MAX_VALID,
} D3D12_PIPELINE_STATE_SUBOBJECT_TYPE;

////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//
// D3D12 Caps
//
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

typedef enum D3D12_FEATURE
{
    D3D12_FEATURE_D3D12_OPTIONS                         =  0,
    D3D12_FEATURE_ARCHITECTURE                          =  1, // Deprecated by D3D12_FEATURE_ARCHITECTURE1
    D3D12_FEATURE_FEATURE_LEVELS                        =  2,
    D3D12_FEATURE_FORMAT_SUPPORT                        =  3,
    D3D12_FEATURE_MULTISAMPLE_QUALITY_LEVELS            =  4,
    D3D12_FEATURE_FORMAT_INFO                           =  5,
    D3D12_FEATURE_GPU_VIRTUAL_ADDRESS_SUPPORT           =  6,
    D3D12_FEATURE_SHADER_MODEL                          =  7,
    D3D12_FEATURE_D3D12_OPTIONS1                        =  8,
    D3D12_FEATURE_PROTECTED_RESOURCE_SESSION_SUPPORT    = 10,
    D3D12_FEATURE_ROOT_SIGNATURE                        = 12,
    D3D12_FEATURE_ARCHITECTURE1                         = 16,
    D3D12_FEATURE_D3D12_OPTIONS2                        = 18,
    D3D12_FEATURE_SHADER_CACHE                          = 19,
    D3D12_FEATURE_COMMAND_QUEUE_PRIORITY                = 20,
    D3D12_FEATURE_D3D12_OPTIONS3                        = 21,
    D3D12_FEATURE_EXISTING_HEAPS                        = 22,
    D3D12_FEATURE_D3D12_OPTIONS4                        = 23,
    D3D12_FEATURE_SERIALIZATION                         = 24,
    D3D12_FEATURE_CROSS_NODE                            = 25,
    D3D12_FEATURE_D3D12_OPTIONS5                        = 27,
    D3D12_FEATURE_DISPLAYABLE                           = 28,
    D3D12_FEATURE_D3D12_OPTIONS6                        = 30,
    D3D12_FEATURE_QUERY_META_COMMAND                    = 31,
    D3D12_FEATURE_D3D12_OPTIONS7                        = 32,
    D3D12_FEATURE_PROTECTED_RESOURCE_SESSION_TYPE_COUNT = 33, 
    D3D12_FEATURE_PROTECTED_RESOURCE_SESSION_TYPES      = 34,
    D3D12_FEATURE_D3D12_OPTIONS8                        = 36,
    D3D12_FEATURE_D3D12_OPTIONS9                        = 37,
    D3D12_FEATURE_D3D12_OPTIONS10                       = 39,
    D3D12_FEATURE_D3D12_OPTIONS11                       = 40,
    D3D12_FEATURE_D3D12_OPTIONS12                       = 41,
    D3D12_FEATURE_D3D12_OPTIONS13                       = 42,
    D3D12_FEATURE_D3D12_OPTIONS14                       = 43,
    D3D12_FEATURE_D3D12_OPTIONS15                       = 44,
    D3D12_FEATURE_D3D12_OPTIONS16                       = 45,
    D3D12_FEATURE_D3D12_OPTIONS17                       = 46,
    D3D12_FEATURE_D3D12_OPTIONS18                       = 47,
    D3D12_FEATURE_D3D12_OPTIONS19                       = 48,
    D3D12_FEATURE_D3D12_OPTIONS20                       = 49,
    D3D12_FEATURE_PREDICATION                           = 50,
    D3D12_FEATURE_PLACED_RESOURCE_SUPPORT_INFO          = 51,
    D3D12_FEATURE_HARDWARE_COPY                         = 52,
    D3D12_FEATURE_D3D12_OPTIONS21                       = 53,
} D3D12_FEATURE;

typedef enum D3D12_SHADER_MIN_PRECISION_SUPPORT
{
    D3D12_SHADER_MIN_PRECISION_SUPPORT_NONE   = 0x0,
    D3D12_SHADER_MIN_PRECISION_SUPPORT_10_BIT = 0x1,
    D3D12_SHADER_MIN_PRECISION_SUPPORT_16_BIT = 0x2
} D3D12_SHADER_MIN_PRECISION_SUPPORT;
cpp_quote( "DEFINE_ENUM_FLAG_OPERATORS( D3D12_SHADER_MIN_PRECISION_SUPPORT )" )

typedef enum D3D12_TILED_RESOURCES_TIER
{
    D3D12_TILED_RESOURCES_TIER_NOT_SUPPORTED = 0,
    D3D12_TILED_RESOURCES_TIER_1        = 1,
    D3D12_TILED_RESOURCES_TIER_2        = 2,
    D3D12_TILED_RESOURCES_TIER_3        = 3,
    D3D12_TILED_RESOURCES_TIER_4        = 4,
} D3D12_TILED_RESOURCES_TIER;

typedef enum D3D12_RESOURCE_BINDING_TIER
{
    D3D12_RESOURCE_BINDING_TIER_1 = 1,
    D3D12_RESOURCE_BINDING_TIER_2 = 2,
    D3D12_RESOURCE_BINDING_TIER_3 = 3,
} D3D12_RESOURCE_BINDING_TIER;

typedef enum D3D12_CONSERVATIVE_RASTERIZATION_TIER { 
  D3D12_CONSERVATIVE_RASTERIZATION_TIER_NOT_SUPPORTED  = 0,
  D3D12_CONSERVATIVE_RASTERIZATION_TIER_1         = 1,
  D3D12_CONSERVATIVE_RASTERIZATION_TIER_2         = 2,
  D3D12_CONSERVATIVE_RASTERIZATION_TIER_3         = 3,
} D3D12_CONSERVATIVE_RASTERIZATION_TIER;


typedef enum D3D12_FORMAT_SUPPORT1
{
    D3D12_FORMAT_SUPPORT1_NONE                       = 0,
    D3D12_FORMAT_SUPPORT1_BUFFER                      = 0x00000001,
    D3D12_FORMAT_SUPPORT1_IA_VERTEX_BUFFER            = 0x00000002,
    D3D12_FORMAT_SUPPORT1_IA_INDEX_BUFFER             = 0x00000004,
    D3D12_FORMAT_SUPPORT1_SO_BUFFER                   = 0x00000008,
    D3D12_FORMAT_SUPPORT1_TEXTURE1D                   = 0x00000010,
    D3D12_FORMAT_SUPPORT1_TEXTURE2D                   = 0x00000020,
    D3D12_FORMAT_SUPPORT1_TEXTURE3D                   = 0x00000040,
    D3D12_FORMAT_SUPPORT1_TEXTURECUBE                 = 0x00000080,
    D3D12_FORMAT_SUPPORT1_SHADER_LOAD                 = 0x00000100,
    D3D12_FORMAT_SUPPORT1_SHADER_SAMPLE               = 0x00000200,
    D3D12_FORMAT_SUPPORT1_SHADER_SAMPLE_COMPARISON    = 0x00000400,
    D3D12_FORMAT_SUPPORT1_SHADER_SAMPLE_MONO_TEXT     = 0x00000800,
    D3D12_FORMAT_SUPPORT1_MIP                         = 0x00001000,
    D3D12_FORMAT_SUPPORT1_RENDER_TARGET               = 0x00004000,
    D3D12_FORMAT_SUPPORT1_BLENDABLE                   = 0x00008000,
    D3D12_FORMAT_SUPPORT1_DEPTH_STENCIL               = 0x00010000,
    D3D12_FORMAT_SUPPORT1_MULTISAMPLE_RESOLVE         = 0x00040000,
    D3D12_FORMAT_SUPPORT1_DISPLAY                     = 0x00080000,
    D3D12_FORMAT_SUPPORT1_CAST_WITHIN_BIT_LAYOUT      = 0x00100000,
    D3D12_FORMAT_SUPPORT1_MULTISAMPLE_RENDERTARGET    = 0x00200000,
    D3D12_FORMAT_SUPPORT1_MULTISAMPLE_LOAD            = 0x00400000,
    D3D12_FORMAT_SUPPORT1_SHADER_GATHER               = 0x00800000,
    D3D12_FORMAT_SUPPORT1_BACK_BUFFER_CAST            = 0x01000000,
    D3D12_FORMAT_SUPPORT1_TYPED_UNORDERED_ACCESS_VIEW = 0x02000000,
    D3D12_FORMAT_SUPPORT1_SHADER_GATHER_COMPARISON    = 0x04000000,
    D3D12_FORMAT_SUPPORT1_DECODER_OUTPUT              = 0x08000000,
    D3D12_FORMAT_SUPPORT1_VIDEO_PROCESSOR_OUTPUT      = 0x10000000,
    D3D12_FORMAT_SUPPORT1_VIDEO_PROCESSOR_INPUT       = 0x20000000,
    D3D12_FORMAT_SUPPORT1_VIDEO_ENCODER               = 0x40000000,
} D3D12_FORMAT_SUPPORT1;
cpp_quote( "DEFINE_ENUM_FLAG_OPERATORS( D3D12_FORMAT_SUPPORT1 )" )

typedef enum D3D12_FORMAT_SUPPORT2
{
    D3D12_FORMAT_SUPPORT2_NONE                                              = 0,
    D3D12_FORMAT_SUPPORT2_UAV_ATOMIC_ADD                                    = 0x00000001,
    D3D12_FORMAT_SUPPORT2_UAV_ATOMIC_BITWISE_OPS                            = 0x00000002,
    D3D12_FORMAT_SUPPORT2_UAV_ATOMIC_COMPARE_STORE_OR_COMPARE_EXCHANGE      = 0x00000004,
    D3D12_FORMAT_SUPPORT2_UAV_ATOMIC_EXCHANGE                               = 0x00000008,
    D3D12_FORMAT_SUPPORT2_UAV_ATOMIC_SIGNED_MIN_OR_MAX                      = 0x00000010,
    D3D12_FORMAT_SUPPORT2_UAV_ATOMIC_UNSIGNED_MIN_OR_MAX                    = 0x00000020,
    D3D12_FORMAT_SUPPORT2_UAV_TYPED_LOAD                                    = 0x00000040,
    D3D12_FORMAT_SUPPORT2_UAV_TYPED_STORE                                   = 0x00000080,
    D3D12_FORMAT_SUPPORT2_OUTPUT_MERGER_LOGIC_OP                            = 0x00000100,
    D3D12_FORMAT_SUPPORT2_TILED                                             = 0x00000200,
    D3D12_FORMAT_SUPPORT2_MULTIPLANE_OVERLAY                                = 0x00004000,
    D3D12_FORMAT_SUPPORT2_SAMPLER_FEEDBACK                                  = 0x00008000,
} D3D12_FORMAT_SUPPORT2;
cpp_quote( "DEFINE_ENUM_FLAG_OPERATORS( D3D12_FORMAT_SUPPORT2 )" )

typedef enum D3D12_MULTISAMPLE_QUALITY_LEVEL_FLAGS
{
    D3D12_MULTISAMPLE_QUALITY_LEVELS_FLAG_NONE           = 0,
    D3D12_MULTISAMPLE_QUALITY_LEVELS_FLAG_TILED_RESOURCE = 0x00000001,
} D3D12_MULTISAMPLE_QUALITY_LEVEL_FLAGS;
cpp_quote( "DEFINE_ENUM_FLAG_OPERATORS( D3D12_MULTISAMPLE_QUALITY_LEVEL_FLAGS )" )

typedef enum D3D12_CROSS_NODE_SHARING_TIER
{
    D3D12_CROSS_NODE_SHARING_TIER_NOT_SUPPORTED    = 0,
    D3D12_CROSS_NODE_SHARING_TIER_1_EMULATED  = 1,
    D3D12_CROSS_NODE_SHARING_TIER_1           = 2,
    D3D12_CROSS_NODE_SHARING_TIER_2           = 3,
    D3D12_CROSS_NODE_SHARING_TIER_3           = 4,
} D3D12_CROSS_NODE_SHARING_TIER;

typedef enum D3D12_RESOURCE_HEAP_TIER
{
    D3D12_RESOURCE_HEAP_TIER_1 = 1,
    D3D12_RESOURCE_HEAP_TIER_2 = 2,
} D3D12_RESOURCE_HEAP_TIER;

typedef enum D3D12_PROGRAMMABLE_SAMPLE_POSITIONS_TIER
{
    D3D12_PROGRAMMABLE_SAMPLE_POSITIONS_TIER_NOT_SUPPORTED = 0,
    D3D12_PROGRAMMABLE_SAMPLE_POSITIONS_TIER_1 = 1,
    D3D12_PROGRAMMABLE_SAMPLE_POSITIONS_TIER_2 = 2,
} D3D12_PROGRAMMABLE_SAMPLE_POSITIONS_TIER;

typedef enum D3D12_VIEW_INSTANCING_TIER
{
    D3D12_VIEW_INSTANCING_TIER_NOT_SUPPORTED = 0,
    D3D12_VIEW_INSTANCING_TIER_1 = 1,
    D3D12_VIEW_INSTANCING_TIER_2 = 2,
    D3D12_VIEW_INSTANCING_TIER_3 = 3,
} D3D12_VIEW_INSTANCING_TIER;

typedef enum D3D12_WORK_GRAPHS_TIER {
    D3D12_WORK_GRAPHS_TIER_NOT_SUPPORTED = 0,
    D3D12_WORK_GRAPHS_TIER_1_0 = 10,
} D3D12_WORK_GRAPHS_TIER;

// D3D12_FEATURE_D3D12_OPTIONS
typedef struct D3D12_FEATURE_DATA_D3D12_OPTIONS
{
    [annotation("_Out_")] BOOL DoublePrecisionFloatShaderOps;
    [annotation("_Out_")] BOOL OutputMergerLogicOp;
    [annotation("_Out_")] D3D12_SHADER_MIN_PRECISION_SUPPORT MinPrecisionSupport;
    [annotation("_Out_")] D3D12_TILED_RESOURCES_TIER TiledResourcesTier;
    [annotation("_Out_")] D3D12_RESOURCE_BINDING_TIER ResourceBindingTier;
    [annotation("_Out_")] BOOL PSSpecifiedStencilRefSupported;
    [annotation("_Out_")] BOOL TypedUAVLoadAdditionalFormats;
    [annotation("_Out_")] BOOL ROVsSupported;
    [annotation("_Out_")] D3D12_CONSERVATIVE_RASTERIZATION_TIER ConservativeRasterizationTier;
    [annotation("_Out_")] UINT MaxGPUVirtualAddressBitsPerResource;
    [annotation("_Out_")] BOOL StandardSwizzle64KBSupported;
    [annotation("_Out_")] D3D12_CROSS_NODE_SHARING_TIER CrossNodeSharingTier;
    [annotation("_Out_")] BOOL CrossAdapterRowMajorTextureSupported;
    [annotation("_Out_")] BOOL VPAndRTArrayIndexFromAnyShaderFeedingRasterizerSupportedWithoutGSEmulation;
    [annotation("_Out_")] D3D12_RESOURCE_HEAP_TIER ResourceHeapTier;
} D3D12_FEATURE_DATA_D3D12_OPTIONS;

// D3D12_FEATURE_D3D12_OPTIONS1
typedef struct D3D12_FEATURE_DATA_D3D12_OPTIONS1
{
    [annotation("_Out_")] BOOL WaveOps;
    [annotation("_Out_")] UINT WaveLaneCountMin;
    [annotation("_Out_")] UINT WaveLaneCountMax;
    [annotation("_Out_")] UINT TotalLaneCount;
    [annotation("_Out_")] BOOL ExpandedComputeResourceStates;
    [annotation("_Out_")] BOOL Int64ShaderOps;
} D3D12_FEATURE_DATA_D3D12_OPTIONS1;

// D3D12_FEATURE_D3D12_OPTIONS2
typedef struct D3D12_FEATURE_DATA_D3D12_OPTIONS2
{
    [annotation("_Out_")] BOOL DepthBoundsTestSupported;
    [annotation("_Out_")] D3D12_PROGRAMMABLE_SAMPLE_POSITIONS_TIER ProgrammableSamplePositionsTier;
} D3D12_FEATURE_DATA_D3D12_OPTIONS2;

// D3D12_FEATURE_ROOT_SIGNATURE
typedef enum D3D_ROOT_SIGNATURE_VERSION
{
    D3D_ROOT_SIGNATURE_VERSION_1 = 0x1,
    D3D_ROOT_SIGNATURE_VERSION_1_0 = 0x1,
    D3D_ROOT_SIGNATURE_VERSION_1_1 = 0x2,
    D3D_ROOT_SIGNATURE_VERSION_1_2 = 0x3,
} D3D_ROOT_SIGNATURE_VERSION;

typedef struct D3D12_FEATURE_DATA_ROOT_SIGNATURE
{
    [annotation("_Inout_")] D3D_ROOT_SIGNATURE_VERSION HighestVersion;
} D3D12_FEATURE_DATA_ROOT_SIGNATURE;

// D3D12_FEATURE_ARCHITECTURE
typedef struct D3D12_FEATURE_DATA_ARCHITECTURE // Deprecated by D3D12_FEATURE_DATA_ARCHITECTURE1
{
    [annotation("_In_")]  UINT NodeIndex;
    [annotation("_Out_")] BOOL TileBasedRenderer;
    [annotation("_Out_")] BOOL UMA; // Needed for applications to understand WRITE_COMBINE
    [annotation("_Out_")] BOOL CacheCoherentUMA; // Only TRUE when UMA is also TRUE
} D3D12_FEATURE_DATA_ARCHITECTURE;

// D3D12_FEATURE_ARCHITECTURE1
typedef struct D3D12_FEATURE_DATA_ARCHITECTURE1
{
    [annotation("_In_")]  UINT NodeIndex;
    [annotation("_Out_")] BOOL TileBasedRenderer;
    [annotation("_Out_")] BOOL UMA; // Needed for applications to understand WRITE_COMBINE
    [annotation("_Out_")] BOOL CacheCoherentUMA; // Only TRUE when UMA is also TRUE
    [annotation("_Out_")] BOOL IsolatedMMU; // GPU accesses don't honor the same MMU functionality as the CPU
} D3D12_FEATURE_DATA_ARCHITECTURE1;

// D3D12_FEATURE_FEATURE_LEVELS
typedef struct D3D12_FEATURE_DATA_FEATURE_LEVELS
{
    [annotation("_In_")] UINT NumFeatureLevels;
    [annotation("_In_reads_(NumFeatureLevels)")] const D3D_FEATURE_LEVEL* pFeatureLevelsRequested;
    [annotation("_Out_")] D3D_FEATURE_LEVEL MaxSupportedFeatureLevel;
} D3D12_FEATURE_DATA_FEATURE_LEVELS;

// D3D_SHADER_MODEL
typedef enum D3D_SHADER_MODEL
{
    D3D_SHADER_MODEL_NONE = 0x0, // e.g MCDM Generic devices
    D3D_SHADER_MODEL_5_1 = 0x51,
    D3D_SHADER_MODEL_6_0 = 0x60,
    D3D_SHADER_MODEL_6_1 = 0x61,
    D3D_SHADER_MODEL_6_2 = 0x62,
    D3D_SHADER_MODEL_6_3 = 0x63,
    D3D_SHADER_MODEL_6_4 = 0x64,
    D3D_SHADER_MODEL_6_5 = 0x65,
    D3D_SHADER_MODEL_6_6 = 0x66,
    D3D_SHADER_MODEL_6_7 = 0x67,
    D3D_SHADER_MODEL_6_8 = 0x68,
    D3D_SHADER_MODEL_6_9 = 0x69,
    D3D_HIGHEST_SHADER_MODEL = D3D_SHADER_MODEL_6_9
} D3D_SHADER_MODEL;

typedef struct D3D12_FEATURE_DATA_SHADER_MODEL
{
    [annotation("_Inout_")] D3D_SHADER_MODEL HighestShaderModel;
} D3D12_FEATURE_DATA_SHADER_MODEL;

// D3D12_FEATURE_FORMAT_SUPPORT
typedef struct D3D12_FEATURE_DATA_FORMAT_SUPPORT
{
    [annotation("_In_")] DXGI_FORMAT Format;
    [annotation("_Out_")] D3D12_FORMAT_SUPPORT1 Support1;
    [annotation("_Out_")] D3D12_FORMAT_SUPPORT2 Support2;
} D3D12_FEATURE_DATA_FORMAT_SUPPORT;

// D3D12_FEATURE_MULTISAMPLE_QUALITY_LEVELS
typedef struct D3D12_FEATURE_DATA_MULTISAMPLE_QUALITY_LEVELS
{
    [annotation("_In_")] DXGI_FORMAT Format;
    [annotation("_In_")] UINT SampleCount;
    [annotation("_In_")] D3D12_MULTISAMPLE_QUALITY_LEVEL_FLAGS Flags;
    [annotation("_Out_")] UINT NumQualityLevels;
} D3D12_FEATURE_DATA_MULTISAMPLE_QUALITY_LEVELS;

// D3D12_FEATURE_FORMAT_INFO
typedef struct D3D12_FEATURE_DATA_FORMAT_INFO
{
    DXGI_FORMAT Format;
    UINT8 PlaneCount;
} D3D12_FEATURE_DATA_FORMAT_INFO;

// D3D12_FEATURE_GPU_VIRTUAL_ADDRESS_SUPPORT
typedef struct D3D12_FEATURE_DATA_GPU_VIRTUAL_ADDRESS_SUPPORT
{
    UINT MaxGPUVirtualAddressBitsPerResource;
    UINT MaxGPUVirtualAddressBitsPerProcess;
} D3D12_FEATURE_DATA_GPU_VIRTUAL_ADDRESS_SUPPORT;


typedef enum D3D12_SHADER_CACHE_SUPPORT_FLAGS
{
    D3D12_SHADER_CACHE_SUPPORT_NONE                     = 0x0,
    D3D12_SHADER_CACHE_SUPPORT_SINGLE_PSO               = 0x1, // Always supported
    D3D12_SHADER_CACHE_SUPPORT_LIBRARY                  = 0x2,
    D3D12_SHADER_CACHE_SUPPORT_AUTOMATIC_INPROC_CACHE   = 0x4,
    D3D12_SHADER_CACHE_SUPPORT_AUTOMATIC_DISK_CACHE     = 0x8,
    D3D12_SHADER_CACHE_SUPPORT_DRIVER_MANAGED_CACHE     = 0x10,
    D3D12_SHADER_CACHE_SUPPORT_SHADER_CONTROL_CLEAR     = 0x20,
    D3D12_SHADER_CACHE_SUPPORT_SHADER_SESSION_DELETE    = 0x40
} D3D12_SHADER_CACHE_SUPPORT_FLAGS;
cpp_quote( "DEFINE_ENUM_FLAG_OPERATORS( D3D12_SHADER_CACHE_SUPPORT_FLAGS )" )

// D3D12_FEATURE_SHADER_CACHE
typedef struct D3D12_FEATURE_DATA_SHADER_CACHE
{
    [annotation("_Out_")] D3D12_SHADER_CACHE_SUPPORT_FLAGS SupportFlags;
} D3D12_FEATURE_DATA_SHADER_CACHE;

// D3D12_FEATURE_COMMAND_QUEUE_PRIORITY
typedef struct D3D12_FEATURE_DATA_COMMAND_QUEUE_PRIORITY
{
    [annotation("_In_")] D3D12_COMMAND_LIST_TYPE CommandListType;
    [annotation("_In_")] UINT Priority;
    [annotation("_Out_")] BOOL PriorityForTypeIsSupported;
} D3D12_FEATURE_DATA_COMMAND_QUEUE_PRIORITY;

typedef enum D3D12_COMMAND_LIST_SUPPORT_FLAGS
{
    D3D12_COMMAND_LIST_SUPPORT_FLAG_NONE                = 0x00000000,
    D3D12_COMMAND_LIST_SUPPORT_FLAG_DIRECT              = 1 << D3D12_COMMAND_LIST_TYPE_DIRECT,
    D3D12_COMMAND_LIST_SUPPORT_FLAG_BUNDLE              = 1 << D3D12_COMMAND_LIST_TYPE_BUNDLE,
    D3D12_COMMAND_LIST_SUPPORT_FLAG_COMPUTE             = 1 << D3D12_COMMAND_LIST_TYPE_COMPUTE,
    D3D12_COMMAND_LIST_SUPPORT_FLAG_COPY                = 1 << D3D12_COMMAND_LIST_TYPE_COPY,
    D3D12_COMMAND_LIST_SUPPORT_FLAG_VIDEO_DECODE        = 1 << D3D12_COMMAND_LIST_TYPE_VIDEO_DECODE,
    D3D12_COMMAND_LIST_SUPPORT_FLAG_VIDEO_PROCESS       = 1 << D3D12_COMMAND_LIST_TYPE_VIDEO_PROCESS,
    D3D12_COMMAND_LIST_SUPPORT_FLAG_VIDEO_ENCODE        = 1 << D3D12_COMMAND_LIST_TYPE_VIDEO_ENCODE,

} D3D12_COMMAND_LIST_SUPPORT_FLAGS;
cpp_quote("DEFINE_ENUM_FLAG_OPERATORS( D3D12_COMMAND_LIST_SUPPORT_FLAGS )")

// D3D12_FEATURE_D3D12_OPTIONS3
typedef struct D3D12_FEATURE_DATA_D3D12_OPTIONS3
{
    [annotation("_Out_")] BOOL CopyQueueTimestampQueriesSupported;
    [annotation("_Out_")] BOOL CastingFullyTypedFormatSupported;
    [annotation("_Out_")] D3D12_COMMAND_LIST_SUPPORT_FLAGS WriteBufferImmediateSupportFlags;
    [annotation("_Out_")] D3D12_VIEW_INSTANCING_TIER ViewInstancingTier;
    [annotation("_Out_")] BOOL BarycentricsSupported;
} D3D12_FEATURE_DATA_D3D12_OPTIONS3;

// D3D12_FEATURE_EXISTING_HEAPS
typedef struct D3D12_FEATURE_DATA_EXISTING_HEAPS
{
    [annotation("_Out_")] BOOL Supported;
} D3D12_FEATURE_DATA_EXISTING_HEAPS;

typedef enum D3D12_SHARED_RESOURCE_COMPATIBILITY_TIER
{
    D3D12_SHARED_RESOURCE_COMPATIBILITY_TIER_0, 
    D3D12_SHARED_RESOURCE_COMPATIBILITY_TIER_1,
    D3D12_SHARED_RESOURCE_COMPATIBILITY_TIER_2,
} D3D12_SHARED_RESOURCE_COMPATIBILITY_TIER;

typedef struct D3D12_FEATURE_DATA_DISPLAYABLE
{
    [annotation("_Out_")] BOOL DisplayableTexture;
    [annotation("_Out_")] D3D12_SHARED_RESOURCE_COMPATIBILITY_TIER SharedResourceCompatibilityTier;
} D3D12_FEATURE_DATA_DISPLAYABLE;

// D3D12_FEATURE_D3D12_OPTIONS4
typedef struct D3D12_FEATURE_DATA_D3D12_OPTIONS4
{
    [annotation("_Out_")] BOOL MSAA64KBAlignedTextureSupported;
    [annotation("_Out_")] D3D12_SHARED_RESOURCE_COMPATIBILITY_TIER SharedResourceCompatibilityTier;
    [annotation("_Out_")] BOOL Native16BitShaderOpsSupported;
} D3D12_FEATURE_DATA_D3D12_OPTIONS4;

typedef enum D3D12_HEAP_SERIALIZATION_TIER
{
    D3D12_HEAP_SERIALIZATION_TIER_0 = 0, 
    D3D12_HEAP_SERIALIZATION_TIER_10 = 10,
} D3D12_HEAP_SERIALIZATION_TIER;

// D3D12_FEATURE_SERIALIZATION
typedef struct D3D12_FEATURE_DATA_SERIALIZATION
{
    [annotation("_In_")]  UINT NodeIndex;
    [annotation("_Out_")] D3D12_HEAP_SERIALIZATION_TIER HeapSerializationTier;
} D3D12_FEATURE_DATA_SERIALIZATION;

// D3D12_FEATURE_CROSS_NODE
typedef struct D3D12_FEATURE_DATA_CROSS_NODE
{
    D3D12_CROSS_NODE_SHARING_TIER SharingTier;
    BOOL AtomicShaderInstructions;
} D3D12_FEATURE_DATA_CROSS_NODE;


typedef enum D3D12_RENDER_PASS_TIER
{
    D3D12_RENDER_PASS_TIER_0 = 0,
    D3D12_RENDER_PASS_TIER_1 = 1,
    D3D12_RENDER_PASS_TIER_2 = 2,


} D3D12_RENDER_PASS_TIER;

typedef enum D3D12_RAYTRACING_TIER
{
    D3D12_RAYTRACING_TIER_NOT_SUPPORTED = 0,
    D3D12_RAYTRACING_TIER_1_0 = 10,
    D3D12_RAYTRACING_TIER_1_1 = 11,
} D3D12_RAYTRACING_TIER;

// D3D12_FEATURE_D3D12_OPTIONS5
typedef struct D3D12_FEATURE_DATA_D3D12_OPTIONS5
{
    [annotation("_Out_")] BOOL SRVOnlyTiledResourceTier3;
    [annotation("_Out_")] D3D12_RENDER_PASS_TIER RenderPassesTier;
    [annotation("_Out_")] D3D12_RAYTRACING_TIER RaytracingTier;
} D3D12_FEATURE_DATA_D3D12_OPTIONS5;

typedef enum D3D12_VARIABLE_SHADING_RATE_TIER
{
    D3D12_VARIABLE_SHADING_RATE_TIER_NOT_SUPPORTED = 0,
    D3D12_VARIABLE_SHADING_RATE_TIER_1 = 1,
    D3D12_VARIABLE_SHADING_RATE_TIER_2 = 2,
} D3D12_VARIABLE_SHADING_RATE_TIER;

// D3D12_FEATURE_D3D12_OPTIONS6
typedef struct D3D12_FEATURE_DATA_D3D12_OPTIONS6
{
    [annotation("_Out_")] BOOL AdditionalShadingRatesSupported;
    [annotation("_Out_")] BOOL PerPrimitiveShadingRateSupportedWithViewportIndexing;
    [annotation("_Out_")] D3D12_VARIABLE_SHADING_RATE_TIER VariableShadingRateTier;
    [annotation("_Out_")] UINT ShadingRateImageTileSize;
    [annotation("_Out_")] BOOL BackgroundProcessingSupported;
} D3D12_FEATURE_DATA_D3D12_OPTIONS6;

typedef enum D3D12_MESH_SHADER_TIER
{
    D3D12_MESH_SHADER_TIER_NOT_SUPPORTED = 0,
    D3D12_MESH_SHADER_TIER_1 = 10,
} D3D12_MESH_SHADER_TIER;

typedef enum D3D12_SAMPLER_FEEDBACK_TIER
{
    D3D12_SAMPLER_FEEDBACK_TIER_NOT_SUPPORTED = 0,
    D3D12_SAMPLER_FEEDBACK_TIER_0_9 = 90,
    D3D12_SAMPLER_FEEDBACK_TIER_1_0 = 100
} D3D12_SAMPLER_FEEDBACK_TIER;

// D3D12_FEATURE_D3D12_OPTIONS7
typedef struct D3D12_FEATURE_DATA_D3D12_OPTIONS7
{
    [annotation("_Out_")] D3D12_MESH_SHADER_TIER MeshShaderTier;
    [annotation("_Out_")] D3D12_SAMPLER_FEEDBACK_TIER SamplerFeedbackTier;

} D3D12_FEATURE_DATA_D3D12_OPTIONS7;

typedef struct D3D12_FEATURE_DATA_QUERY_META_COMMAND
{
    [annotation("_In_")] GUID CommandId;
    [annotation("_In_")] UINT NodeMask;
    [annotation("_Field_size_bytes_full_opt_( QueryInputDataSizeInBytes )")] const void* pQueryInputData;
    [annotation("_In_")] SIZE_T QueryInputDataSizeInBytes;
    [annotation("_Field_size_bytes_full_( QueryOutputDataSizeInBytes )")] void* pQueryOutputData;
    [annotation("_In_")] SIZE_T QueryOutputDataSizeInBytes;
} D3D12_FEATURE_DATA_QUERY_META_COMMAND;

// D3D12_FEATURE_D3D12_OPTIONS8
typedef struct D3D12_FEATURE_DATA_D3D12_OPTIONS8
{
    [annotation("_Out_")] BOOL UnalignedBlockTexturesSupported;
} D3D12_FEATURE_DATA_D3D12_OPTIONS8;

typedef enum D3D12_WAVE_MMA_TIER {
    D3D12_WAVE_MMA_TIER_NOT_SUPPORTED = 0,
    D3D12_WAVE_MMA_TIER_1_0 = 10,
} D3D12_WAVE_MMA_TIER;

// D3D12_FEATURE_D3D12_OPTIONS9
typedef struct D3D12_FEATURE_DATA_D3D12_OPTIONS9
{
    [annotation("_Out_")] BOOL MeshShaderPipelineStatsSupported;
    [annotation("_Out_")] BOOL MeshShaderSupportsFullRangeRenderTargetArrayIndex;
    [annotation("_Out_")] BOOL AtomicInt64OnTypedResourceSupported;
    [annotation("_Out_")] BOOL AtomicInt64OnGroupSharedSupported;
    [annotation("_Out_")] BOOL DerivativesInMeshAndAmplificationShadersSupported;
    [annotation("_Out_")] D3D12_WAVE_MMA_TIER WaveMMATier;
} D3D12_FEATURE_DATA_D3D12_OPTIONS9;

// D3D12_FEATURE_D3D12_OPTIONS10
typedef struct D3D12_FEATURE_DATA_D3D12_OPTIONS10
{
    [annotation("_Out_")] BOOL VariableRateShadingSumCombinerSupported;
    [annotation("_Out_")] BOOL MeshShaderPerPrimitiveShadingRateSupported;
} D3D12_FEATURE_DATA_D3D12_OPTIONS10;

// D3D12_FEATURE_D3D12_OPTIONS11
typedef struct D3D12_FEATURE_DATA_D3D12_OPTIONS11
{
    [annotation("_Out_")] BOOL AtomicInt64OnDescriptorHeapResourceSupported;
} D3D12_FEATURE_DATA_D3D12_OPTIONS11;

typedef enum D3D12_TRI_STATE {
    D3D12_TRI_STATE_UNKNOWN = -1,
    D3D12_TRI_STATE_FALSE = 0,
    D3D12_TRI_STATE_TRUE = 1,
} D3D12_TRI_STATE;

// D3D12_FEATURE_D3D12_OPTIONS12
typedef struct D3D12_FEATURE_DATA_D3D12_OPTIONS12
{
    [annotation("_Out_")] D3D12_TRI_STATE MSPrimitivesPipelineStatisticIncludesCulledPrimitives;
    [annotation("_Out_")] BOOL EnhancedBarriersSupported;
    [annotation("_Out_")] BOOL RelaxedFormatCastingSupported;
} D3D12_FEATURE_DATA_D3D12_OPTIONS12;

// D3D12_FEATURE_D3D12_OPTIONS13
typedef struct D3D12_FEATURE_DATA_D3D12_OPTIONS13
{
    [annotation("_Out_")] BOOL UnrestrictedBufferTextureCopyPitchSupported;
    [annotation("_Out_")] BOOL UnrestrictedVertexElementAlignmentSupported;
    [annotation("_Out_")] BOOL InvertedViewportHeightFlipsYSupported;
    [annotation("_Out_")] BOOL InvertedViewportDepthFlipsZSupported;
    [annotation("_Out_")] BOOL TextureCopyBetweenDimensionsSupported;
    [annotation("_Out_")] BOOL AlphaBlendFactorSupported;   
} D3D12_FEATURE_DATA_D3D12_OPTIONS13;

// D3D12_FEATURE_D3D12_OPTIONS14
typedef struct D3D12_FEATURE_DATA_D3D12_OPTIONS14
{
    [annotation("_Out_")] BOOL AdvancedTextureOpsSupported;
    [annotation("_Out_")] BOOL WriteableMSAATexturesSupported;
    [annotation("_Out_")] BOOL IndependentFrontAndBackStencilRefMaskSupported;
} D3D12_FEATURE_DATA_D3D12_OPTIONS14;

// D3D12_FEATURE_D3D12_OPTIONS15
typedef struct D3D12_FEATURE_DATA_D3D12_OPTIONS15
{
    [annotation("_Out_")] BOOL TriangleFanSupported;
    [annotation("_Out_")] BOOL DynamicIndexBufferStripCutSupported;
} D3D12_FEATURE_DATA_D3D12_OPTIONS15;

// D3D12_FEATURE_D3D12_OPTIONS16
typedef struct D3D12_FEATURE_DATA_D3D12_OPTIONS16
{
    [annotation("_Out_")] BOOL DynamicDepthBiasSupported;
    [annotation("_Out_")] BOOL GPUUploadHeapSupported;
} D3D12_FEATURE_DATA_D3D12_OPTIONS16;

// D3D12_FEATURE_D3D12_OPTIONS17
typedef struct D3D12_FEATURE_DATA_D3D12_OPTIONS17
{
    [annotation("_Out_")] BOOL NonNormalizedCoordinateSamplersSupported;
    [annotation("_Out_")] BOOL ManualWriteTrackingResourceSupported;
} D3D12_FEATURE_DATA_D3D12_OPTIONS17;

// D3D12_FEATURE_D3D12_OPTIONS18
typedef struct D3D12_FEATURE_DATA_D3D12_OPTIONS18
{
    [annotation("_Out_")] BOOL RenderPassesValid;
} D3D12_FEATURE_DATA_D3D12_OPTIONS18;

// D3D12_FEATURE_D3D12_OPTIONS19
typedef struct D3D12_FEATURE_DATA_D3D12_OPTIONS19
{
    BOOL MismatchingOutputDimensionsSupported;
    UINT SupportedSampleCountsWithNoOutputs;
    BOOL PointSamplingAddressesNeverRoundUp;
    BOOL RasterizerDesc2Supported;
    BOOL NarrowQuadrilateralLinesSupported;
    BOOL AnisoFilterWithPointMipSupported;
    UINT MaxSamplerDescriptorHeapSize;
    UINT MaxSamplerDescriptorHeapSizeWithStaticSamplers;
    UINT MaxViewDescriptorHeapSize;
    [annotation("_Out_")] BOOL ComputeOnlyCustomHeapSupported;
} D3D12_FEATURE_DATA_D3D12_OPTIONS19;


typedef enum D3D12_RECREATE_AT_TIER
{
    D3D12_RECREATE_AT_TIER_NOT_SUPPORTED = 0,

    // * Supports retrieving resource and heap allocation information 
    //   with ID3D12PageableTools::GetAllocationInfo
    // * Supports setting resource and heap virtual addresses with
    //   ID3D12DeviceTools::SetNextAllocation
    D3D12_RECREATE_AT_TIER_1 = 1,

} D3D12_RECREATE_AT_TIER;


// D3D12_FEATURE_D3D12_OPTIONS20
typedef struct D3D12_FEATURE_DATA_D3D12_OPTIONS20
{
    [annotation("_Out_")] BOOL ComputeOnlyWriteWatchSupported;
    D3D12_RECREATE_AT_TIER RecreateAtTier;
} D3D12_FEATURE_DATA_D3D12_OPTIONS20;

typedef enum D3D12_EXECUTE_INDIRECT_TIER
{
    D3D12_EXECUTE_INDIRECT_TIER_1_0 = 10,
    D3D12_EXECUTE_INDIRECT_TIER_1_1 = 11,
} D3D12_EXECUTE_INDIRECT_TIER;

typedef struct D3D12_FEATURE_DATA_D3D12_OPTIONS21
{
    [annotation("_Out_")] D3D12_WORK_GRAPHS_TIER WorkGraphsTier;
    [annotation("_Out_")] D3D12_EXECUTE_INDIRECT_TIER ExecuteIndirectTier;
    [annotation("_Out_")] BOOL SampleCmpGradientAndBiasSupported;
    [annotation("_Out_")] BOOL ExtendedCommandInfoSupported;
} D3D12_FEATURE_DATA_D3D12_OPTIONS21;


typedef struct D3D12_FEATURE_DATA_PREDICATION
{
    [annotation("_Out_")] BOOL Supported;
} D3D12_FEATURE_DATA_PREDICATION;

typedef struct D3D12_FEATURE_DATA_HARDWARE_COPY
{
    [annotation("_Out_")] BOOL Supported;
} D3D12_FEATURE_DATA_HARDWARE_COPY;


typedef struct D3D12_RESOURCE_ALLOCATION_INFO
{
    UINT64 SizeInBytes;
    UINT64 Alignment;
} D3D12_RESOURCE_ALLOCATION_INFO;

typedef struct D3D12_RESOURCE_ALLOCATION_INFO1
{
    UINT64 Offset;
    UINT64 Alignment;
    UINT64 SizeInBytes;
} D3D12_RESOURCE_ALLOCATION_INFO1;

typedef enum D3D12_HEAP_TYPE
{
    D3D12_HEAP_TYPE_DEFAULT   = 1, 
    D3D12_HEAP_TYPE_UPLOAD    = 2,
    D3D12_HEAP_TYPE_READBACK  = 3,
    D3D12_HEAP_TYPE_CUSTOM    = 4,
    D3D12_HEAP_TYPE_GPU_UPLOAD = 5,
} D3D12_HEAP_TYPE;

typedef enum D3D12_CPU_PAGE_PROPERTY
{
    D3D12_CPU_PAGE_PROPERTY_UNKNOWN = 0,
    D3D12_CPU_PAGE_PROPERTY_NOT_AVAILABLE = 1,
    D3D12_CPU_PAGE_PROPERTY_WRITE_COMBINE = 2,
    D3D12_CPU_PAGE_PROPERTY_WRITE_BACK = 3,
} D3D12_CPU_PAGE_PROPERTY;

typedef enum D3D12_MEMORY_POOL
{
    D3D12_MEMORY_POOL_UNKNOWN = 0,
    D3D12_MEMORY_POOL_L0 = 1, // Maximum bandwidth for CPU
    D3D12_MEMORY_POOL_L1 = 2, // More bandwidth for GPU, less for CPU
} D3D12_MEMORY_POOL;

typedef struct D3D12_HEAP_PROPERTIES
{
    D3D12_HEAP_TYPE Type;
    D3D12_CPU_PAGE_PROPERTY CPUPageProperty;
    D3D12_MEMORY_POOL MemoryPoolPreference;
    UINT CreationNodeMask;
    UINT VisibleNodeMask;
} D3D12_HEAP_PROPERTIES;

typedef enum D3D12_HEAP_FLAGS
{
    D3D12_HEAP_FLAG_NONE = 0x0,
    D3D12_HEAP_FLAG_SHARED = 0x1,
    D3D12_HEAP_FLAG_DENY_BUFFERS = 0x4,
    D3D12_HEAP_FLAG_ALLOW_DISPLAY = 0x8,
    D3D12_HEAP_FLAG_SHARED_CROSS_ADAPTER = 0x20,
    D3D12_HEAP_FLAG_DENY_RT_DS_TEXTURES = 0x40,
    D3D12_HEAP_FLAG_DENY_NON_RT_DS_TEXTURES = 0x80,
    D3D12_HEAP_FLAG_HARDWARE_PROTECTED = 0x100,
    D3D12_HEAP_FLAG_ALLOW_WRITE_WATCH = 0x200,
    D3D12_HEAP_FLAG_ALLOW_SHADER_ATOMICS = 0x400,
    D3D12_HEAP_FLAG_CREATE_NOT_RESIDENT = 0x800,
    D3D12_HEAP_FLAG_CREATE_NOT_ZEROED = 0x1000,
    D3D12_HEAP_FLAG_TOOLS_USE_MANUAL_WRITE_TRACKING = 0x2000,

    // These are convenience aliases to manage resource heap tier restrictions. They cannot be bitwise OR'ed together cleanly.
    D3D12_HEAP_FLAG_ALLOW_ALL_BUFFERS_AND_TEXTURES = 0x0,
    D3D12_HEAP_FLAG_ALLOW_ONLY_BUFFERS = 0xC0,
    D3D12_HEAP_FLAG_ALLOW_ONLY_NON_RT_DS_TEXTURES = 0x44,
    D3D12_HEAP_FLAG_ALLOW_ONLY_RT_DS_TEXTURES = 0x84,


} D3D12_HEAP_FLAGS;
cpp_quote( "DEFINE_ENUM_FLAG_OPERATORS( D3D12_HEAP_FLAGS )" )

typedef struct D3D12_HEAP_DESC 
{
    UINT64 SizeInBytes;
    D3D12_HEAP_PROPERTIES Properties;
    UINT64 Alignment;
    D3D12_HEAP_FLAGS Flags;
} D3D12_HEAP_DESC;

typedef enum D3D12_RESOURCE_DIMENSION
{
    D3D12_RESOURCE_DIMENSION_UNKNOWN = 0,
    D3D12_RESOURCE_DIMENSION_BUFFER = 1,
    D3D12_RESOURCE_DIMENSION_TEXTURE1D = 2,
    D3D12_RESOURCE_DIMENSION_TEXTURE2D = 3,
    D3D12_RESOURCE_DIMENSION_TEXTURE3D = 4,
} D3D12_RESOURCE_DIMENSION;

typedef struct D3D12_FEATURE_DATA_PLACED_RESOURCE_SUPPORT_INFO
{
    [annotation("_In_")] DXGI_FORMAT Format;
    [annotation("_In_")] D3D12_RESOURCE_DIMENSION Dimension;
    [annotation("_In_")] D3D12_HEAP_PROPERTIES DestHeapProperties;
    [annotation("_Out_")] BOOL Supported;
} D3D12_FEATURE_DATA_PLACED_RESOURCE_SUPPORT_INFO;

typedef enum D3D12_TEXTURE_LAYOUT
{
    D3D12_TEXTURE_LAYOUT_UNKNOWN = 0,
    D3D12_TEXTURE_LAYOUT_ROW_MAJOR = 1, // Must be used with buffers.  Can be used with cross-adapter shared textures.
    D3D12_TEXTURE_LAYOUT_64KB_UNDEFINED_SWIZZLE = 2,
    D3D12_TEXTURE_LAYOUT_64KB_STANDARD_SWIZZLE = 3,
} D3D12_TEXTURE_LAYOUT; 

typedef enum D3D12_RESOURCE_FLAGS
{
    D3D12_RESOURCE_FLAG_NONE = 0x0,
    D3D12_RESOURCE_FLAG_ALLOW_RENDER_TARGET = 0x1,
    D3D12_RESOURCE_FLAG_ALLOW_DEPTH_STENCIL = 0x2,
    D3D12_RESOURCE_FLAG_ALLOW_UNORDERED_ACCESS = 0x4,
    D3D12_RESOURCE_FLAG_DENY_SHADER_RESOURCE = 0x8,
    D3D12_RESOURCE_FLAG_ALLOW_CROSS_ADAPTER = 0x10,
    D3D12_RESOURCE_FLAG_ALLOW_SIMULTANEOUS_ACCESS = 0x20,
    D3D12_RESOURCE_FLAG_VIDEO_DECODE_REFERENCE_ONLY = 0x40, 
    D3D12_RESOURCE_FLAG_VIDEO_ENCODE_REFERENCE_ONLY = 0x80, 
    D3D12_RESOURCE_FLAG_RAYTRACING_ACCELERATION_STRUCTURE = 0x100,

} D3D12_RESOURCE_FLAGS;
cpp_quote( "DEFINE_ENUM_FLAG_OPERATORS( D3D12_RESOURCE_FLAGS )" )

typedef struct D3D12_MIP_REGION
{
    UINT Width;
    UINT Height;
    UINT Depth;
} D3D12_MIP_REGION;

typedef struct D3D12_RESOURCE_DESC
{
    D3D12_RESOURCE_DIMENSION Dimension;
    UINT64 Alignment;
    UINT64 Width;
    UINT Height;
    UINT16 DepthOrArraySize;
    UINT16 MipLevels;
    DXGI_FORMAT Format;
    DXGI_SAMPLE_DESC SampleDesc;
    D3D12_TEXTURE_LAYOUT Layout;
    D3D12_RESOURCE_FLAGS Flags;
} D3D12_RESOURCE_DESC;

typedef struct D3D12_RESOURCE_DESC1
{
    D3D12_RESOURCE_DIMENSION Dimension;
    UINT64 Alignment;
    UINT64 Width;
    UINT Height;
    UINT16 DepthOrArraySize;
    UINT16 MipLevels;
    DXGI_FORMAT Format;
    DXGI_SAMPLE_DESC SampleDesc;
    D3D12_TEXTURE_LAYOUT Layout;
    D3D12_RESOURCE_FLAGS Flags;
    D3D12_MIP_REGION SamplerFeedbackMipRegion;
} D3D12_RESOURCE_DESC1;



typedef struct D3D12_DEPTH_STENCIL_VALUE
{
    FLOAT Depth;
    UINT8 Stencil;
} D3D12_DEPTH_STENCIL_VALUE;

typedef struct D3D12_CLEAR_VALUE
{
    DXGI_FORMAT Format;
    union
    {
        FLOAT Color[4];
        D3D12_DEPTH_STENCIL_VALUE DepthStencil;
    };
} D3D12_CLEAR_VALUE;

typedef struct D3D12_RANGE
{
    SIZE_T Begin;
    SIZE_T End; // One past end, so (End - Begin) = Size
} D3D12_RANGE;

typedef struct D3D12_RANGE_UINT64
{
    UINT64 Begin;
    UINT64 End; // One past end, so (End - Begin) = Size
} D3D12_RANGE_UINT64;

typedef struct D3D12_SUBRESOURCE_RANGE_UINT64
{
    UINT Subresource;
    D3D12_RANGE_UINT64 Range;
} D3D12_SUBRESOURCE_RANGE_UINT64;

typedef struct D3D12_SUBRESOURCE_INFO
{
    UINT64 Offset;
    UINT RowPitch;
    UINT DepthPitch;
} D3D12_SUBRESOURCE_INFO;

typedef struct D3D12_TILED_RESOURCE_COORDINATE
{
    UINT X;
    UINT Y;
    UINT Z;
    UINT Subresource; 
} D3D12_TILED_RESOURCE_COORDINATE;

typedef struct D3D12_TILE_REGION_SIZE
{
    UINT NumTiles; 
    BOOL UseBox; 
    UINT Width; 
    UINT16 Height;
    UINT16 Depth;
} D3D12_TILE_REGION_SIZE;

typedef enum D3D12_TILE_RANGE_FLAGS
{
    D3D12_TILE_RANGE_FLAG_NONE = 0,
    D3D12_TILE_RANGE_FLAG_NULL = 1,
    D3D12_TILE_RANGE_FLAG_SKIP = 2,
    D3D12_TILE_RANGE_FLAG_REUSE_SINGLE_TILE = 4,

} D3D12_TILE_RANGE_FLAGS;

typedef struct D3D12_SUBRESOURCE_TILING
{
    UINT WidthInTiles;
    UINT16 HeightInTiles;
    UINT16 DepthInTiles;
    UINT StartTileIndexInOverallResource;
} D3D12_SUBRESOURCE_TILING;

typedef struct D3D12_TILE_SHAPE
{
    UINT WidthInTexels; 
    UINT HeightInTexels;
    UINT DepthInTexels;
} D3D12_TILE_SHAPE;

typedef struct D3D12_PACKED_MIP_INFO
{
    UINT8 NumStandardMips;
    UINT8 NumPackedMips;
    UINT NumTilesForPackedMips;
    UINT StartTileIndexInOverallResource;
} D3D12_PACKED_MIP_INFO;

typedef enum D3D12_TILE_MAPPING_FLAGS 
{
    D3D12_TILE_MAPPING_FLAG_NONE = 0x0,
    D3D12_TILE_MAPPING_FLAG_NO_HAZARD = 0x1,
} D3D12_TILE_MAPPING_FLAGS;
cpp_quote( "DEFINE_ENUM_FLAG_OPERATORS( D3D12_TILE_MAPPING_FLAGS )" )

typedef enum D3D12_TILE_COPY_FLAGS 
{
    D3D12_TILE_COPY_FLAG_NONE = 0x0,
    D3D12_TILE_COPY_FLAG_NO_HAZARD = 0x1,
    D3D12_TILE_COPY_FLAG_LINEAR_BUFFER_TO_SWIZZLED_TILED_RESOURCE = 0x2,
    D3D12_TILE_COPY_FLAG_SWIZZLED_TILED_RESOURCE_TO_LINEAR_BUFFER = 0x4,
} D3D12_TILE_COPY_FLAGS;
cpp_quote("DEFINE_ENUM_FLAG_OPERATORS( D3D12_TILE_COPY_FLAGS )")

// Resource Barrier
typedef enum D3D12_RESOURCE_STATES
{
    D3D12_RESOURCE_STATE_COMMON = 0,

    D3D12_RESOURCE_STATE_VERTEX_AND_CONSTANT_BUFFER = 0x00001,
    D3D12_RESOURCE_STATE_INDEX_BUFFER = 0x00002,
    D3D12_RESOURCE_STATE_RENDER_TARGET = 0x00004,
    D3D12_RESOURCE_STATE_UNORDERED_ACCESS = 0x00008,
    D3D12_RESOURCE_STATE_DEPTH_WRITE = 0x00010,
    D3D12_RESOURCE_STATE_DEPTH_READ = 0x00020,
    D3D12_RESOURCE_STATE_NON_PIXEL_SHADER_RESOURCE = 0x00040,
    D3D12_RESOURCE_STATE_PIXEL_SHADER_RESOURCE = 0x00080,
    D3D12_RESOURCE_STATE_STREAM_OUT = 0x00100,
    D3D12_RESOURCE_STATE_INDIRECT_ARGUMENT = 0x00200,
    D3D12_RESOURCE_STATE_COPY_DEST = 0x00400,
    D3D12_RESOURCE_STATE_COPY_SOURCE = 0x00800,
    D3D12_RESOURCE_STATE_RESOLVE_DEST = 0x01000,
    D3D12_RESOURCE_STATE_RESOLVE_SOURCE = 0x02000,
    D3D12_RESOURCE_STATE_RAYTRACING_ACCELERATION_STRUCTURE = 0x400000,
    D3D12_RESOURCE_STATE_SHADING_RATE_SOURCE = 0x1000000,

    D3D12_RESOURCE_STATE_RESERVED_INTERNAL_8000 = 0x8000,
    D3D12_RESOURCE_STATE_RESERVED_INTERNAL_4000 = 0x4000,
    D3D12_RESOURCE_STATE_RESERVED_INTERNAL_100000 = 0x100000,
    D3D12_RESOURCE_STATE_RESERVED_INTERNAL_40000000 = 0x40000000,
    D3D12_RESOURCE_STATE_RESERVED_INTERNAL_80000000 = 0x80000000,

    D3D12_RESOURCE_STATE_GENERIC_READ =
        /*D3D12_RESOURCE_STATE_VERTEX_AND_CONSTANT_BUFFER*/ 0x0001 |
        /*D3D12_RESOURCE_STATE_INDEX_BUFFER*/               0x0002 |
        /*D3D12_RESOURCE_STATE_NON_PIXEL_SHADER_RESOURCE*/  0x0040 |
        /*D3D12_RESOURCE_STATE_PIXEL_SHADER_RESOURCE*/      0x0080 |
        /*D3D12_RESOURCE_STATE_INDIRECT_ARGUMENT*/          0x0200 |
        /*D3D12_RESOURCE_STATE_COPY_SOURCE*/                0x0800,
        
    D3D12_RESOURCE_STATE_ALL_SHADER_RESOURCE =
        /*D3D12_RESOURCE_STATE_NON_PIXEL_SHADER_RESOURCE*/ 0x40 |
        /*D3D12_RESOURCE_STATE_PIXEL_SHADER_RESOURCE*/     0x80,

    D3D12_RESOURCE_STATE_PRESENT                = 0,
    D3D12_RESOURCE_STATE_PREDICATION            = /*D3D12_RESOURCE_STATE_INDIRECT_ARGUMENT*/ 0x0200,

    D3D12_RESOURCE_STATE_VIDEO_DECODE_READ          = 0x00010000,
    D3D12_RESOURCE_STATE_VIDEO_DECODE_WRITE         = 0x00020000,
    D3D12_RESOURCE_STATE_VIDEO_PROCESS_READ         = 0x00040000,
    D3D12_RESOURCE_STATE_VIDEO_PROCESS_WRITE        = 0x00080000,    
    D3D12_RESOURCE_STATE_VIDEO_ENCODE_READ          = 0x00200000,
    D3D12_RESOURCE_STATE_VIDEO_ENCODE_WRITE         = 0x00800000,

} D3D12_RESOURCE_STATES;
cpp_quote( "DEFINE_ENUM_FLAG_OPERATORS( D3D12_RESOURCE_STATES )" )

typedef enum D3D12_RESOURCE_BARRIER_TYPE
{
    D3D12_RESOURCE_BARRIER_TYPE_TRANSITION,
    D3D12_RESOURCE_BARRIER_TYPE_ALIASING,
    D3D12_RESOURCE_BARRIER_TYPE_UAV,
} D3D12_RESOURCE_BARRIER_TYPE;

interface ID3D12Resource;

typedef struct D3D12_RESOURCE_TRANSITION_BARRIER
{
    ID3D12Resource* pResource;
    UINT Subresource;
    D3D12_RESOURCE_STATES StateBefore;
    D3D12_RESOURCE_STATES StateAfter;
} D3D12_RESOURCE_TRANSITION_BARRIER;

typedef struct D3D12_RESOURCE_ALIASING_BARRIER
{
    ID3D12Resource* pResourceBefore;
    ID3D12Resource* pResourceAfter;
} D3D12_RESOURCE_ALIASING_BARRIER;

typedef struct D3D12_RESOURCE_UAV_BARRIER
{
    ID3D12Resource* pResource;
} D3D12_RESOURCE_UAV_BARRIER;

typedef enum D3D12_RESOURCE_BARRIER_FLAGS
{
    D3D12_RESOURCE_BARRIER_FLAG_NONE       = 0x0,
    D3D12_RESOURCE_BARRIER_FLAG_BEGIN_ONLY = 0x1,
    D3D12_RESOURCE_BARRIER_FLAG_END_ONLY   = 0x2,
} D3D12_RESOURCE_BARRIER_FLAGS;
cpp_quote( "DEFINE_ENUM_FLAG_OPERATORS( D3D12_RESOURCE_BARRIER_FLAGS )" )

typedef struct D3D12_RESOURCE_BARRIER
{
    D3D12_RESOURCE_BARRIER_TYPE    Type;
    D3D12_RESOURCE_BARRIER_FLAGS    Flags;

    union
    {
        D3D12_RESOURCE_TRANSITION_BARRIER Transition;
        D3D12_RESOURCE_ALIASING_BARRIER   Aliasing;
        D3D12_RESOURCE_UAV_BARRIER        UAV;
    };
} D3D12_RESOURCE_BARRIER;

typedef struct D3D12_SUBRESOURCE_FOOTPRINT
{
    DXGI_FORMAT Format;
    UINT        Width;
    UINT        Height;
    UINT        Depth;
    UINT        RowPitch; // Must be a multiple of D3D12_TEXTURE_DATA_PITCH_ALIGNMENT
} D3D12_SUBRESOURCE_FOOTPRINT;

typedef struct D3D12_PLACED_SUBRESOURCE_FOOTPRINT
{
    UINT64                         Offset; // Must be a multiple of D3D12_TEXTURE_DATA_PLACEMENT_ALIGNMENT
    D3D12_SUBRESOURCE_FOOTPRINT Footprint;
} D3D12_PLACED_SUBRESOURCE_FOOTPRINT;

typedef enum D3D12_TEXTURE_COPY_TYPE
{
    D3D12_TEXTURE_COPY_TYPE_SUBRESOURCE_INDEX  = 0,
    D3D12_TEXTURE_COPY_TYPE_PLACED_FOOTPRINT   = 1,
} D3D12_TEXTURE_COPY_TYPE;

typedef struct D3D12_TEXTURE_COPY_LOCATION
{
    ID3D12Resource* pResource;
    D3D12_TEXTURE_COPY_TYPE Type;
    union
    {
        D3D12_PLACED_SUBRESOURCE_FOOTPRINT PlacedFootprint;
        UINT SubresourceIndex;
    };
} D3D12_TEXTURE_COPY_LOCATION;

typedef enum D3D12_RESOLVE_MODE
{
    D3D12_RESOLVE_MODE_DECOMPRESS = 0,
    D3D12_RESOLVE_MODE_MIN = 1,
    D3D12_RESOLVE_MODE_MAX = 2,
    D3D12_RESOLVE_MODE_AVERAGE = 3,
    D3D12_RESOLVE_MODE_ENCODE_SAMPLER_FEEDBACK = 4,
    D3D12_RESOLVE_MODE_DECODE_SAMPLER_FEEDBACK = 5
} D3D12_RESOLVE_MODE;

typedef struct D3D12_SAMPLE_POSITION
{
    INT8 X;
    INT8 Y;
} D3D12_SAMPLE_POSITION;

typedef struct D3D12_VIEW_INSTANCE_LOCATION
{
    UINT ViewportArrayIndex;
    UINT RenderTargetArrayIndex;
} D3D12_VIEW_INSTANCE_LOCATION;

typedef enum D3D12_VIEW_INSTANCING_FLAGS
{
    D3D12_VIEW_INSTANCING_FLAG_NONE = 0x0,
    D3D12_VIEW_INSTANCING_FLAG_ENABLE_VIEW_INSTANCE_MASKING = 0x1,
} D3D12_VIEW_INSTANCING_FLAGS;
cpp_quote("DEFINE_ENUM_FLAG_OPERATORS( D3D12_VIEW_INSTANCING_FLAGS )")

typedef struct D3D12_VIEW_INSTANCING_DESC
{
    UINT ViewInstanceCount;
    [annotation("_Field_size_full_(ViewInstanceCount)")] const D3D12_VIEW_INSTANCE_LOCATION* pViewInstanceLocations;
    D3D12_VIEW_INSTANCING_FLAGS Flags;
} D3D12_VIEW_INSTANCING_DESC;

// D3D12 view descriptions
typedef enum D3D12_SHADER_COMPONENT_MAPPING
{
    D3D12_SHADER_COMPONENT_MAPPING_FROM_MEMORY_COMPONENT_0 = 0,
    D3D12_SHADER_COMPONENT_MAPPING_FROM_MEMORY_COMPONENT_1 = 1,
    D3D12_SHADER_COMPONENT_MAPPING_FROM_MEMORY_COMPONENT_2 = 2,
    D3D12_SHADER_COMPONENT_MAPPING_FROM_MEMORY_COMPONENT_3 = 3,
    D3D12_SHADER_COMPONENT_MAPPING_FORCE_VALUE_0 = 4,
    D3D12_SHADER_COMPONENT_MAPPING_FORCE_VALUE_1 = 5,
} D3D12_SHADER_COMPONENT_MAPPING;

cpp_quote( "#define D3D12_SHADER_COMPONENT_MAPPING_MASK 0x7 ")
cpp_quote( "#define D3D12_SHADER_COMPONENT_MAPPING_SHIFT 3 ")
cpp_quote( "#define D3D12_SHADER_COMPONENT_MAPPING_ALWAYS_SET_BIT_AVOIDING_ZEROMEM_MISTAKES (1<<(D3D12_SHADER_COMPONENT_MAPPING_SHIFT*4)) ")
cpp_quote( "#define D3D12_ENCODE_SHADER_4_COMPONENT_MAPPING(Src0,Src1,Src2,Src3) ((((Src0)&D3D12_SHADER_COMPONENT_MAPPING_MASK)| \\")
cpp_quote( "                                                                (((Src1)&D3D12_SHADER_COMPONENT_MAPPING_MASK)<<D3D12_SHADER_COMPONENT_MAPPING_SHIFT)| \\")
cpp_quote( "                                                                (((Src2)&D3D12_SHADER_COMPONENT_MAPPING_MASK)<<(D3D12_SHADER_COMPONENT_MAPPING_SHIFT*2))| \\")
cpp_quote( "                                                                (((Src3)&D3D12_SHADER_COMPONENT_MAPPING_MASK)<<(D3D12_SHADER_COMPONENT_MAPPING_SHIFT*3))| \\")
cpp_quote( "                                                                D3D12_SHADER_COMPONENT_MAPPING_ALWAYS_SET_BIT_AVOIDING_ZEROMEM_MISTAKES))")
cpp_quote( "#define D3D12_DECODE_SHADER_4_COMPONENT_MAPPING(ComponentToExtract,Mapping) ((D3D12_SHADER_COMPONENT_MAPPING)(Mapping >> (D3D12_SHADER_COMPONENT_MAPPING_SHIFT*ComponentToExtract) & D3D12_SHADER_COMPONENT_MAPPING_MASK))")
cpp_quote( "#define D3D12_DEFAULT_SHADER_4_COMPONENT_MAPPING D3D12_ENCODE_SHADER_4_COMPONENT_MAPPING(0,1,2,3) ")
typedef enum D3D12_BUFFER_SRV_FLAGS
{
    D3D12_BUFFER_SRV_FLAG_NONE = 0,
    D3D12_BUFFER_SRV_FLAG_RAW  = 0x00000001,      // allow device multi-component reads with DWORD addressing
} D3D12_BUFFER_SRV_FLAGS;
cpp_quote( "DEFINE_ENUM_FLAG_OPERATORS( D3D12_BUFFER_SRV_FLAGS )" )

typedef struct D3D12_BUFFER_SRV
{
    UINT64 FirstElement;
    UINT NumElements;
    UINT StructureByteStride; // if nonzero, format must be DXGI_FORMAT_UNKNOWN
    D3D12_BUFFER_SRV_FLAGS Flags;
} D3D12_BUFFER_SRV;

typedef struct D3D12_TEX1D_SRV
{
    UINT MostDetailedMip;
    UINT MipLevels;
    FLOAT ResourceMinLODClamp;
} D3D12_TEX1D_SRV;

typedef struct D3D12_TEX1D_ARRAY_SRV
{
    UINT MostDetailedMip;
    UINT MipLevels;
    UINT FirstArraySlice;
    UINT ArraySize;
    FLOAT ResourceMinLODClamp;
} D3D12_TEX1D_ARRAY_SRV;

typedef struct D3D12_TEX2D_SRV
{
    UINT MostDetailedMip;
    UINT MipLevels;
    UINT PlaneSlice;
    FLOAT ResourceMinLODClamp;
} D3D12_TEX2D_SRV;

typedef struct D3D12_TEX2D_ARRAY_SRV
{
    UINT MostDetailedMip;
    UINT MipLevels;
    UINT FirstArraySlice;
    UINT ArraySize;
    UINT PlaneSlice;
    FLOAT ResourceMinLODClamp;
} D3D12_TEX2D_ARRAY_SRV;

typedef struct D3D12_TEX3D_SRV
{
    UINT MostDetailedMip;
    UINT MipLevels;
    FLOAT ResourceMinLODClamp;
} D3D12_TEX3D_SRV;

typedef struct D3D12_TEXCUBE_SRV
{
    UINT MostDetailedMip;
    UINT MipLevels;
    FLOAT ResourceMinLODClamp;
} D3D12_TEXCUBE_SRV;

typedef struct D3D12_TEXCUBE_ARRAY_SRV
{
    UINT MostDetailedMip;
    UINT MipLevels;
    UINT First2DArrayFace;
    UINT NumCubes;
    FLOAT ResourceMinLODClamp;
} D3D12_TEXCUBE_ARRAY_SRV;

typedef struct D3D12_TEX2DMS_SRV
{
    // don't need to define anything specific for this view dimension
    UINT UnusedField_NothingToDefine;
} D3D12_TEX2DMS_SRV;

typedef struct D3D12_TEX2DMS_ARRAY_SRV
{
    UINT FirstArraySlice;
    UINT ArraySize;
} D3D12_TEX2DMS_ARRAY_SRV;

typedef struct D3D12_RAYTRACING_ACCELERATION_STRUCTURE_SRV
{
    D3D12_GPU_VIRTUAL_ADDRESS Location;
} D3D12_RAYTRACING_ACCELERATION_STRUCTURE_SRV;

typedef enum D3D12_SRV_DIMENSION {
    D3D12_SRV_DIMENSION_UNKNOWN = 0,
    D3D12_SRV_DIMENSION_BUFFER = 1,
    D3D12_SRV_DIMENSION_TEXTURE1D = 2,
    D3D12_SRV_DIMENSION_TEXTURE1DARRAY = 3,
    D3D12_SRV_DIMENSION_TEXTURE2D = 4,
    D3D12_SRV_DIMENSION_TEXTURE2DARRAY = 5,
    D3D12_SRV_DIMENSION_TEXTURE2DMS = 6,
    D3D12_SRV_DIMENSION_TEXTURE2DMSARRAY = 7,
    D3D12_SRV_DIMENSION_TEXTURE3D = 8,
    D3D12_SRV_DIMENSION_TEXTURECUBE = 9,
    D3D12_SRV_DIMENSION_TEXTURECUBEARRAY = 10,
    D3D12_SRV_DIMENSION_RAYTRACING_ACCELERATION_STRUCTURE = 11,
} D3D12_SRV_DIMENSION;

typedef struct D3D12_SHADER_RESOURCE_VIEW_DESC
{
    DXGI_FORMAT Format;
    D3D12_SRV_DIMENSION ViewDimension;
    UINT Shader4ComponentMapping;

    union
    {
        D3D12_BUFFER_SRV Buffer;
        D3D12_TEX1D_SRV Texture1D;
        D3D12_TEX1D_ARRAY_SRV Texture1DArray;
        D3D12_TEX2D_SRV Texture2D;
        D3D12_TEX2D_ARRAY_SRV Texture2DArray;
        D3D12_TEX2DMS_SRV Texture2DMS;
        D3D12_TEX2DMS_ARRAY_SRV Texture2DMSArray;
        D3D12_TEX3D_SRV Texture3D;
        D3D12_TEXCUBE_SRV TextureCube;
        D3D12_TEXCUBE_ARRAY_SRV TextureCubeArray;
        D3D12_RAYTRACING_ACCELERATION_STRUCTURE_SRV RaytracingAccelerationStructure;
    };
} D3D12_SHADER_RESOURCE_VIEW_DESC;

typedef struct D3D12_CONSTANT_BUFFER_VIEW_DESC
{
    D3D12_GPU_VIRTUAL_ADDRESS BufferLocation;
    UINT SizeInBytes; 
} D3D12_CONSTANT_BUFFER_VIEW_DESC;

////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//
// Sampler
//
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
typedef enum D3D12_FILTER
{
    // Bits used in defining enumeration of valid filters:
    // bits [1:0] - mip: 0 == point, 1 == linear, 2,3 unused
    // bits [3:2] - mag: 0 == point, 1 == linear, 2,3 unused
    // bits [5:4] - min: 0 == point, 1 == linear, 2,3 unused
    // bit  [6]   - aniso
    // bits [8:7] - reduction type:
    //                0 == standard filtering
    //                1 == comparison
    //                2 == min
    //                3 == max
    // bit  [31]  - mono 1-bit (narrow-purpose filter) [no longer supported in D3D12]

    D3D12_FILTER_MIN_MAG_MIP_POINT                              = 0x00000000,
    D3D12_FILTER_MIN_MAG_POINT_MIP_LINEAR                       = 0x00000001,
    D3D12_FILTER_MIN_POINT_MAG_LINEAR_MIP_POINT                 = 0x00000004,
    D3D12_FILTER_MIN_POINT_MAG_MIP_LINEAR                       = 0x00000005,
    D3D12_FILTER_MIN_LINEAR_MAG_MIP_POINT                       = 0x00000010,
    D3D12_FILTER_MIN_LINEAR_MAG_POINT_MIP_LINEAR                = 0x00000011,
    D3D12_FILTER_MIN_MAG_LINEAR_MIP_POINT                       = 0x00000014,
    D3D12_FILTER_MIN_MAG_MIP_LINEAR                             = 0x00000015,
    D3D12_FILTER_MIN_MAG_ANISOTROPIC_MIP_POINT                  = 0x00000054,
    D3D12_FILTER_ANISOTROPIC                                    = 0x00000055,
    D3D12_FILTER_COMPARISON_MIN_MAG_MIP_POINT                   = 0x00000080,
    D3D12_FILTER_COMPARISON_MIN_MAG_POINT_MIP_LINEAR            = 0x00000081,
    D3D12_FILTER_COMPARISON_MIN_POINT_MAG_LINEAR_MIP_POINT      = 0x00000084,
    D3D12_FILTER_COMPARISON_MIN_POINT_MAG_MIP_LINEAR            = 0x00000085,
    D3D12_FILTER_COMPARISON_MIN_LINEAR_MAG_MIP_POINT            = 0x00000090,
    D3D12_FILTER_COMPARISON_MIN_LINEAR_MAG_POINT_MIP_LINEAR     = 0x00000091,
    D3D12_FILTER_COMPARISON_MIN_MAG_LINEAR_MIP_POINT            = 0x00000094,
    D3D12_FILTER_COMPARISON_MIN_MAG_MIP_LINEAR                  = 0x00000095,
    D3D12_FILTER_COMPARISON_MIN_MAG_ANISOTROPIC_MIP_POINT       = 0x000000d4,
    D3D12_FILTER_COMPARISON_ANISOTROPIC                         = 0x000000d5,
    D3D12_FILTER_MINIMUM_MIN_MAG_MIP_POINT                      = 0x00000100,
    D3D12_FILTER_MINIMUM_MIN_MAG_POINT_MIP_LINEAR               = 0x00000101,
    D3D12_FILTER_MINIMUM_MIN_POINT_MAG_LINEAR_MIP_POINT         = 0x00000104,
    D3D12_FILTER_MINIMUM_MIN_POINT_MAG_MIP_LINEAR               = 0x00000105,
    D3D12_FILTER_MINIMUM_MIN_LINEAR_MAG_MIP_POINT               = 0x00000110,
    D3D12_FILTER_MINIMUM_MIN_LINEAR_MAG_POINT_MIP_LINEAR        = 0x00000111,
    D3D12_FILTER_MINIMUM_MIN_MAG_LINEAR_MIP_POINT               = 0x00000114,
    D3D12_FILTER_MINIMUM_MIN_MAG_MIP_LINEAR                     = 0x00000115,
    D3D12_FILTER_MINIMUM_MIN_MAG_ANISOTROPIC_MIP_POINT          = 0x00000154,
    D3D12_FILTER_MINIMUM_ANISOTROPIC                            = 0x00000155,
    D3D12_FILTER_MAXIMUM_MIN_MAG_MIP_POINT                      = 0x00000180,
    D3D12_FILTER_MAXIMUM_MIN_MAG_POINT_MIP_LINEAR               = 0x00000181,
    D3D12_FILTER_MAXIMUM_MIN_POINT_MAG_LINEAR_MIP_POINT         = 0x00000184,
    D3D12_FILTER_MAXIMUM_MIN_POINT_MAG_MIP_LINEAR               = 0x00000185,
    D3D12_FILTER_MAXIMUM_MIN_LINEAR_MAG_MIP_POINT               = 0x00000190,
    D3D12_FILTER_MAXIMUM_MIN_LINEAR_MAG_POINT_MIP_LINEAR        = 0x00000191,
    D3D12_FILTER_MAXIMUM_MIN_MAG_LINEAR_MIP_POINT               = 0x00000194,
    D3D12_FILTER_MAXIMUM_MIN_MAG_MIP_LINEAR                     = 0x00000195,
    D3D12_FILTER_MAXIMUM_MIN_MAG_ANISOTROPIC_MIP_POINT          = 0x000001d4,
    D3D12_FILTER_MAXIMUM_ANISOTROPIC                            = 0x000001d5
} D3D12_FILTER;

typedef enum D3D12_FILTER_TYPE
{
    D3D12_FILTER_TYPE_POINT = 0,
    D3D12_FILTER_TYPE_LINEAR = 1,
} D3D12_FILTER_TYPE;

typedef enum D3D12_FILTER_REDUCTION_TYPE
{
    D3D12_FILTER_REDUCTION_TYPE_STANDARD = 0,
    D3D12_FILTER_REDUCTION_TYPE_COMPARISON = 1,
    D3D12_FILTER_REDUCTION_TYPE_MINIMUM = 2,
    D3D12_FILTER_REDUCTION_TYPE_MAXIMUM = 3,
} D3D12_FILTER_REDUCTION_TYPE;

const UINT D3D12_FILTER_REDUCTION_TYPE_MASK = 0x00000003;
const UINT D3D12_FILTER_REDUCTION_TYPE_SHIFT = 7;

const UINT D3D12_FILTER_TYPE_MASK = 0x00000003;
const UINT D3D12_MIN_FILTER_SHIFT = 4;
const UINT D3D12_MAG_FILTER_SHIFT = 2;
const UINT D3D12_MIP_FILTER_SHIFT = 0;

const UINT D3D12_ANISOTROPIC_FILTERING_BIT = 0x00000040;

// encode enum entry for most filters except anisotropic filtering
cpp_quote( "#define D3D12_ENCODE_BASIC_FILTER( min, mag, mip, reduction )                                                     \\" )
cpp_quote( "                                   ( ( D3D12_FILTER ) (                                                           \\" )
cpp_quote( "                                   ( ( ( min ) & D3D12_FILTER_TYPE_MASK ) << D3D12_MIN_FILTER_SHIFT ) |           \\" )
cpp_quote( "                                   ( ( ( mag ) & D3D12_FILTER_TYPE_MASK ) << D3D12_MAG_FILTER_SHIFT ) |           \\" )
cpp_quote( "                                   ( ( ( mip ) & D3D12_FILTER_TYPE_MASK ) << D3D12_MIP_FILTER_SHIFT ) |           \\" )
cpp_quote( "                                   ( ( ( reduction ) & D3D12_FILTER_REDUCTION_TYPE_MASK ) << D3D12_FILTER_REDUCTION_TYPE_SHIFT ) ) ) " )

// encode enum entry for anisotropic filtering (with or without comparison filtering)
cpp_quote( "#define D3D12_ENCODE_ANISOTROPIC_FILTER( reduction )                                                  \\" )
cpp_quote( "                                         ( ( D3D12_FILTER ) (                                         \\" )
cpp_quote( "                                         D3D12_ANISOTROPIC_FILTERING_BIT |                            \\" )
cpp_quote( "                                         D3D12_ENCODE_BASIC_FILTER( D3D12_FILTER_TYPE_LINEAR,         \\" )
cpp_quote( "                                                                    D3D12_FILTER_TYPE_LINEAR,         \\" )
cpp_quote( "                                                                    D3D12_FILTER_TYPE_LINEAR,         \\" )
cpp_quote( "                                                                    reduction ) ) )                     " )
cpp_quote( "#define D3D12_ENCODE_MIN_MAG_ANISOTROPIC_MIP_POINT_FILTER( reduction )                                \\" )
cpp_quote( "                                         ( ( D3D12_FILTER ) (                                         \\" )
cpp_quote( "                                         D3D12_ANISOTROPIC_FILTERING_BIT |                            \\" )
cpp_quote( "                                         D3D12_ENCODE_BASIC_FILTER( D3D12_FILTER_TYPE_LINEAR,         \\" )
cpp_quote( "                                                                    D3D12_FILTER_TYPE_LINEAR,         \\" )
cpp_quote( "                                                                    D3D12_FILTER_TYPE_POINT,          \\" )
cpp_quote( "                                                                    reduction ) ) )                     " )

cpp_quote( "#define D3D12_DECODE_MIN_FILTER( D3D12Filter )                                                              \\" )
cpp_quote( "                                 ( ( D3D12_FILTER_TYPE )                                                    \\" )
cpp_quote( "                                 ( ( ( D3D12Filter ) >> D3D12_MIN_FILTER_SHIFT ) & D3D12_FILTER_TYPE_MASK ) ) " )

cpp_quote( "#define D3D12_DECODE_MAG_FILTER( D3D12Filter )                                                              \\" )
cpp_quote( "                                 ( ( D3D12_FILTER_TYPE )                                                    \\" )
cpp_quote( "                                 ( ( ( D3D12Filter ) >> D3D12_MAG_FILTER_SHIFT ) & D3D12_FILTER_TYPE_MASK ) ) " )

cpp_quote( "#define D3D12_DECODE_MIP_FILTER( D3D12Filter )                                                              \\" )
cpp_quote( "                                 ( ( D3D12_FILTER_TYPE )                                                    \\" )
cpp_quote( "                                 ( ( ( D3D12Filter ) >> D3D12_MIP_FILTER_SHIFT ) & D3D12_FILTER_TYPE_MASK ) ) " )

cpp_quote( "#define D3D12_DECODE_FILTER_REDUCTION( D3D12Filter )                                                        \\" )
cpp_quote( "                                 ( ( D3D12_FILTER_REDUCTION_TYPE )                                                      \\" )
cpp_quote( "                                 ( ( ( D3D12Filter ) >> D3D12_FILTER_REDUCTION_TYPE_SHIFT ) & D3D12_FILTER_REDUCTION_TYPE_MASK ) ) " )

cpp_quote( "#define D3D12_DECODE_IS_COMPARISON_FILTER( D3D12Filter )                                                    \\" )
cpp_quote( "                                 ( D3D12_DECODE_FILTER_REDUCTION( D3D12Filter ) == D3D12_FILTER_REDUCTION_TYPE_COMPARISON ) " )

cpp_quote( "#define D3D12_DECODE_IS_ANISOTROPIC_FILTER( D3D12Filter )                                               \\" )
cpp_quote( "                            ( ( ( D3D12Filter ) & D3D12_ANISOTROPIC_FILTERING_BIT ) &&                  \\" )
cpp_quote( "                            ( D3D12_FILTER_TYPE_LINEAR == D3D12_DECODE_MIN_FILTER( D3D12Filter ) ) &&   \\" )
cpp_quote( "                            ( D3D12_FILTER_TYPE_LINEAR == D3D12_DECODE_MAG_FILTER( D3D12Filter ) ) )      " )

typedef enum D3D12_TEXTURE_ADDRESS_MODE
{
    D3D12_TEXTURE_ADDRESS_MODE_WRAP = 1,
    D3D12_TEXTURE_ADDRESS_MODE_MIRROR = 2,
    D3D12_TEXTURE_ADDRESS_MODE_CLAMP = 3,
    D3D12_TEXTURE_ADDRESS_MODE_BORDER = 4,
    D3D12_TEXTURE_ADDRESS_MODE_MIRROR_ONCE = 5
} D3D12_TEXTURE_ADDRESS_MODE;



typedef struct D3D12_SAMPLER_DESC
{
    D3D12_FILTER Filter;
    D3D12_TEXTURE_ADDRESS_MODE AddressU;
    D3D12_TEXTURE_ADDRESS_MODE AddressV;
    D3D12_TEXTURE_ADDRESS_MODE AddressW;
    FLOAT MipLODBias;
    UINT MaxAnisotropy;
    D3D12_COMPARISON_FUNC ComparisonFunc;
    FLOAT BorderColor[4]; // RGBA
    FLOAT MinLOD;
    FLOAT MaxLOD;
} D3D12_SAMPLER_DESC;


typedef enum D3D12_SAMPLER_FLAGS
{
    D3D12_SAMPLER_FLAG_NONE = 0x0,
    D3D12_SAMPLER_FLAG_UINT_BORDER_COLOR = 0x01,
    D3D12_SAMPLER_FLAG_NON_NORMALIZED_COORDINATES = 0x02,
} D3D12_SAMPLER_FLAGS;
cpp_quote("DEFINE_ENUM_FLAG_OPERATORS( D3D12_SAMPLER_FLAGS )")

typedef struct D3D12_SAMPLER_DESC2
{
    D3D12_FILTER Filter;
    D3D12_TEXTURE_ADDRESS_MODE AddressU;
    D3D12_TEXTURE_ADDRESS_MODE AddressV;
    D3D12_TEXTURE_ADDRESS_MODE AddressW;
    FLOAT MipLODBias;
    UINT MaxAnisotropy;
    D3D12_COMPARISON_FUNC ComparisonFunc;
    union
    {
        FLOAT FloatBorderColor[4]; // RGBA
        UINT  UintBorderColor[4];
    };
    FLOAT MinLOD;
    FLOAT MaxLOD;
    D3D12_SAMPLER_FLAGS Flags;
} D3D12_SAMPLER_DESC2;



typedef enum D3D12_BUFFER_UAV_FLAGS
{
    D3D12_BUFFER_UAV_FLAG_NONE =   0,
    D3D12_BUFFER_UAV_FLAG_RAW  =   0x00000001,
} D3D12_BUFFER_UAV_FLAGS;
cpp_quote( "DEFINE_ENUM_FLAG_OPERATORS( D3D12_BUFFER_UAV_FLAGS )" )

typedef struct D3D12_BUFFER_UAV
{
    UINT64 FirstElement;
    UINT NumElements;
    UINT StructureByteStride; // if nonzero, format must be DXGI_FORMAT_UNKNOWN
    UINT64 CounterOffsetInBytes;
    D3D12_BUFFER_UAV_FLAGS Flags;
} D3D12_BUFFER_UAV;

typedef struct D3D12_TEX1D_UAV
{
    UINT MipSlice;
} D3D12_TEX1D_UAV;

typedef struct D3D12_TEX1D_ARRAY_UAV
{
    UINT MipSlice;
    UINT FirstArraySlice;
    UINT ArraySize;
} D3D12_TEX1D_ARRAY_UAV;

typedef struct D3D12_TEX2D_UAV
{
    UINT MipSlice;
    UINT PlaneSlice;
} D3D12_TEX2D_UAV;

typedef struct D3D12_TEX2D_ARRAY_UAV
{
    UINT MipSlice;
    UINT FirstArraySlice;
    UINT ArraySize;
    UINT PlaneSlice;
} D3D12_TEX2D_ARRAY_UAV;

typedef struct D3D12_TEX2DMS_UAV
{
    // don't need to define anything specific for this view dimension
    UINT UnusedField_NothingToDefine;
} D3D12_TEX2DMS_UAV;

typedef struct D3D12_TEX2DMS_ARRAY_UAV
{
    UINT FirstArraySlice;
    UINT ArraySize;
} D3D12_TEX2DMS_ARRAY_UAV;

typedef struct D3D12_TEX3D_UAV
{
    UINT MipSlice;
    UINT FirstWSlice;
    UINT WSize;
} D3D12_TEX3D_UAV;

typedef enum D3D12_UAV_DIMENSION
{
    D3D12_UAV_DIMENSION_UNKNOWN = 0,
    D3D12_UAV_DIMENSION_BUFFER = 1,
    D3D12_UAV_DIMENSION_TEXTURE1D = 2,
    D3D12_UAV_DIMENSION_TEXTURE1DARRAY = 3,
    D3D12_UAV_DIMENSION_TEXTURE2D = 4,
    D3D12_UAV_DIMENSION_TEXTURE2DARRAY = 5,
    D3D12_UAV_DIMENSION_TEXTURE2DMS = 6,
    D3D12_UAV_DIMENSION_TEXTURE2DMSARRAY = 7,
    D3D12_UAV_DIMENSION_TEXTURE3D = 8,
} D3D12_UAV_DIMENSION;

typedef struct D3D12_UNORDERED_ACCESS_VIEW_DESC
{
    DXGI_FORMAT Format;
    D3D12_UAV_DIMENSION ViewDimension;

    union
    {
        D3D12_BUFFER_UAV Buffer;
        D3D12_TEX1D_UAV Texture1D;
        D3D12_TEX1D_ARRAY_UAV Texture1DArray;
        D3D12_TEX2D_UAV Texture2D;
        D3D12_TEX2D_ARRAY_UAV Texture2DArray;
        D3D12_TEX2DMS_UAV Texture2DMS;
        D3D12_TEX2DMS_ARRAY_UAV Texture2DMSArray;
        D3D12_TEX3D_UAV Texture3D;
    };
} D3D12_UNORDERED_ACCESS_VIEW_DESC;

typedef struct D3D12_BUFFER_RTV
{
    UINT64 FirstElement;
    UINT NumElements;
} D3D12_BUFFER_RTV;

typedef struct D3D12_TEX1D_RTV
{
    UINT MipSlice;
} D3D12_TEX1D_RTV;

typedef struct D3D12_TEX1D_ARRAY_RTV
{
    UINT MipSlice;
    UINT FirstArraySlice;
    UINT ArraySize;
} D3D12_TEX1D_ARRAY_RTV;

typedef struct D3D12_TEX2D_RTV
{
    UINT MipSlice;
    UINT PlaneSlice;
} D3D12_TEX2D_RTV;

typedef struct D3D12_TEX2DMS_RTV
{
    UINT UnusedField_NothingToDefine;
} D3D12_TEX2DMS_RTV;

typedef struct D3D12_TEX2D_ARRAY_RTV
{
    UINT MipSlice;
    UINT FirstArraySlice;
    UINT ArraySize;
    UINT PlaneSlice;
} D3D12_TEX2D_ARRAY_RTV;

typedef struct D3D12_TEX2DMS_ARRAY_RTV
{
    UINT FirstArraySlice;
    UINT ArraySize;
} D3D12_TEX2DMS_ARRAY_RTV;

typedef struct D3D12_TEX3D_RTV
{
    UINT MipSlice;
    UINT FirstWSlice;
    UINT WSize;
} D3D12_TEX3D_RTV;

typedef enum D3D12_RTV_DIMENSION
{
    D3D12_RTV_DIMENSION_UNKNOWN = 0,
    D3D12_RTV_DIMENSION_BUFFER = 1,
    D3D12_RTV_DIMENSION_TEXTURE1D = 2,
    D3D12_RTV_DIMENSION_TEXTURE1DARRAY = 3,
    D3D12_RTV_DIMENSION_TEXTURE2D = 4,
    D3D12_RTV_DIMENSION_TEXTURE2DARRAY = 5,
    D3D12_RTV_DIMENSION_TEXTURE2DMS = 6,
    D3D12_RTV_DIMENSION_TEXTURE2DMSARRAY = 7,
    D3D12_RTV_DIMENSION_TEXTURE3D = 8,
} D3D12_RTV_DIMENSION;

typedef struct D3D12_RENDER_TARGET_VIEW_DESC
{
    DXGI_FORMAT Format;
    D3D12_RTV_DIMENSION ViewDimension;

    union
    {
        D3D12_BUFFER_RTV Buffer;
        D3D12_TEX1D_RTV Texture1D;
        D3D12_TEX1D_ARRAY_RTV Texture1DArray;
        D3D12_TEX2D_RTV Texture2D;
        D3D12_TEX2D_ARRAY_RTV Texture2DArray;
        D3D12_TEX2DMS_RTV Texture2DMS;
        D3D12_TEX2DMS_ARRAY_RTV Texture2DMSArray;
        D3D12_TEX3D_RTV Texture3D;
    };
} D3D12_RENDER_TARGET_VIEW_DESC;

typedef struct D3D12_TEX1D_DSV
{
    UINT MipSlice;
} D3D12_TEX1D_DSV;

typedef struct D3D12_TEX1D_ARRAY_DSV
{
    UINT MipSlice;
    UINT FirstArraySlice;
    UINT ArraySize;
} D3D12_TEX1D_ARRAY_DSV;

typedef struct D3D12_TEX2D_DSV
{
    UINT MipSlice;
} D3D12_TEX2D_DSV;

typedef struct D3D12_TEX2D_ARRAY_DSV
{
    UINT MipSlice;
    UINT FirstArraySlice;
    UINT ArraySize;
} D3D12_TEX2D_ARRAY_DSV;

typedef struct D3D12_TEX2DMS_DSV
{
    UINT UnusedField_NothingToDefine;
} D3D12_TEX2DMS_DSV;

typedef struct D3D12_TEX2DMS_ARRAY_DSV
{
    UINT FirstArraySlice;
    UINT ArraySize;
} D3D12_TEX2DMS_ARRAY_DSV;

typedef enum D3D12_DSV_FLAGS
{
    D3D12_DSV_FLAG_NONE              = 0x0,
    D3D12_DSV_FLAG_READ_ONLY_DEPTH   = 0x1,
    D3D12_DSV_FLAG_READ_ONLY_STENCIL = 0x2,
} D3D12_DSV_FLAGS;
cpp_quote( "DEFINE_ENUM_FLAG_OPERATORS( D3D12_DSV_FLAGS )" )

typedef enum D3D12_DSV_DIMENSION
{
    D3D12_DSV_DIMENSION_UNKNOWN = 0,
    D3D12_DSV_DIMENSION_TEXTURE1D = 1,
    D3D12_DSV_DIMENSION_TEXTURE1DARRAY = 2,
    D3D12_DSV_DIMENSION_TEXTURE2D = 3,
    D3D12_DSV_DIMENSION_TEXTURE2DARRAY = 4,
    D3D12_DSV_DIMENSION_TEXTURE2DMS = 5,
    D3D12_DSV_DIMENSION_TEXTURE2DMSARRAY = 6,
} D3D12_DSV_DIMENSION;

typedef struct D3D12_DEPTH_STENCIL_VIEW_DESC
{
    DXGI_FORMAT Format;
    D3D12_DSV_DIMENSION ViewDimension;
    D3D12_DSV_FLAGS Flags;

    union
    {
        D3D12_TEX1D_DSV Texture1D;
        D3D12_TEX1D_ARRAY_DSV Texture1DArray;
        D3D12_TEX2D_DSV Texture2D;
        D3D12_TEX2D_ARRAY_DSV Texture2DArray;
        D3D12_TEX2DMS_DSV Texture2DMS;
        D3D12_TEX2DMS_ARRAY_DSV Texture2DMSArray;
    };
} D3D12_DEPTH_STENCIL_VIEW_DESC;

typedef enum D3D12_CLEAR_FLAGS
{
    // Intentionally no flag for NONE
    D3D12_CLEAR_FLAG_DEPTH   = 0x01,
    D3D12_CLEAR_FLAG_STENCIL = 0x02,
} D3D12_CLEAR_FLAGS;
cpp_quote( "DEFINE_ENUM_FLAG_OPERATORS( D3D12_CLEAR_FLAGS )" )

typedef enum D3D12_FENCE_FLAGS
{
    D3D12_FENCE_FLAG_NONE = 0x0,
    D3D12_FENCE_FLAG_SHARED = 0x1,
    D3D12_FENCE_FLAG_SHARED_CROSS_ADAPTER = 0x2,
    D3D12_FENCE_FLAG_NON_MONITORED = 0x4,
} D3D12_FENCE_FLAGS;
cpp_quote( "DEFINE_ENUM_FLAG_OPERATORS( D3D12_FENCE_FLAGS )" )

typedef enum D3D12_DESCRIPTOR_HEAP_TYPE
{
    D3D12_DESCRIPTOR_HEAP_TYPE_CBV_SRV_UAV,
    D3D12_DESCRIPTOR_HEAP_TYPE_SAMPLER,
    D3D12_DESCRIPTOR_HEAP_TYPE_RTV,
    D3D12_DESCRIPTOR_HEAP_TYPE_DSV,
    D3D12_DESCRIPTOR_HEAP_TYPE_NUM_TYPES
} D3D12_DESCRIPTOR_HEAP_TYPE;

typedef enum D3D12_DESCRIPTOR_HEAP_FLAGS
{
    D3D12_DESCRIPTOR_HEAP_FLAG_NONE = 0x0,
    D3D12_DESCRIPTOR_HEAP_FLAG_SHADER_VISIBLE = 0x1,
} D3D12_DESCRIPTOR_HEAP_FLAGS;
cpp_quote( "DEFINE_ENUM_FLAG_OPERATORS( D3D12_DESCRIPTOR_HEAP_FLAGS )" )

typedef struct D3D12_DESCRIPTOR_HEAP_DESC
{
    D3D12_DESCRIPTOR_HEAP_TYPE Type;
    UINT NumDescriptors;
    D3D12_DESCRIPTOR_HEAP_FLAGS Flags;
    UINT NodeMask;
} D3D12_DESCRIPTOR_HEAP_DESC;

typedef enum D3D12_DESCRIPTOR_RANGE_TYPE
{
    D3D12_DESCRIPTOR_RANGE_TYPE_SRV,
    D3D12_DESCRIPTOR_RANGE_TYPE_UAV,
    D3D12_DESCRIPTOR_RANGE_TYPE_CBV,
    D3D12_DESCRIPTOR_RANGE_TYPE_SAMPLER
} D3D12_DESCRIPTOR_RANGE_TYPE;

typedef struct D3D12_DESCRIPTOR_RANGE
{
    D3D12_DESCRIPTOR_RANGE_TYPE RangeType;
    UINT NumDescriptors;
    UINT BaseShaderRegister;
    UINT RegisterSpace;
    UINT OffsetInDescriptorsFromTableStart;
} D3D12_DESCRIPTOR_RANGE;


typedef struct D3D12_ROOT_DESCRIPTOR_TABLE
{
    UINT NumDescriptorRanges;
    [annotation("_Field_size_full_(NumDescriptorRanges)")] const D3D12_DESCRIPTOR_RANGE* pDescriptorRanges;
} D3D12_ROOT_DESCRIPTOR_TABLE;


typedef struct D3D12_ROOT_CONSTANTS
{
    UINT ShaderRegister;
    UINT RegisterSpace;
    UINT Num32BitValues;
} D3D12_ROOT_CONSTANTS;

typedef struct D3D12_ROOT_DESCRIPTOR
{
    UINT ShaderRegister;
    UINT RegisterSpace;
} D3D12_ROOT_DESCRIPTOR;

typedef enum D3D12_SHADER_VISIBILITY
{
    D3D12_SHADER_VISIBILITY_ALL = 0,
    D3D12_SHADER_VISIBILITY_VERTEX = 1,
    D3D12_SHADER_VISIBILITY_HULL = 2,
    D3D12_SHADER_VISIBILITY_DOMAIN = 3,
    D3D12_SHADER_VISIBILITY_GEOMETRY = 4,
    D3D12_SHADER_VISIBILITY_PIXEL = 5,
    D3D12_SHADER_VISIBILITY_AMPLIFICATION = 6,
    D3D12_SHADER_VISIBILITY_MESH = 7,
} D3D12_SHADER_VISIBILITY;

typedef enum D3D12_ROOT_PARAMETER_TYPE
{
    D3D12_ROOT_PARAMETER_TYPE_DESCRIPTOR_TABLE,
    D3D12_ROOT_PARAMETER_TYPE_32BIT_CONSTANTS,
    D3D12_ROOT_PARAMETER_TYPE_CBV,
    D3D12_ROOT_PARAMETER_TYPE_SRV,
    D3D12_ROOT_PARAMETER_TYPE_UAV
} D3D12_ROOT_PARAMETER_TYPE;

typedef struct D3D12_ROOT_PARAMETER
{
    D3D12_ROOT_PARAMETER_TYPE ParameterType;
    union
    {
        D3D12_ROOT_DESCRIPTOR_TABLE    DescriptorTable;
        D3D12_ROOT_CONSTANTS           Constants;
        D3D12_ROOT_DESCRIPTOR          Descriptor;
    };
    D3D12_SHADER_VISIBILITY ShaderVisibility;
} D3D12_ROOT_PARAMETER;

typedef enum D3D12_ROOT_SIGNATURE_FLAGS
{
    D3D12_ROOT_SIGNATURE_FLAG_NONE                                  = 0x0,
    D3D12_ROOT_SIGNATURE_FLAG_ALLOW_INPUT_ASSEMBLER_INPUT_LAYOUT    = 0x1,
    D3D12_ROOT_SIGNATURE_FLAG_DENY_VERTEX_SHADER_ROOT_ACCESS        = 0x2,
    D3D12_ROOT_SIGNATURE_FLAG_DENY_HULL_SHADER_ROOT_ACCESS          = 0x4,
    D3D12_ROOT_SIGNATURE_FLAG_DENY_DOMAIN_SHADER_ROOT_ACCESS        = 0x8,
    D3D12_ROOT_SIGNATURE_FLAG_DENY_GEOMETRY_SHADER_ROOT_ACCESS      = 0x10,
    D3D12_ROOT_SIGNATURE_FLAG_DENY_PIXEL_SHADER_ROOT_ACCESS         = 0x20,
    D3D12_ROOT_SIGNATURE_FLAG_ALLOW_STREAM_OUTPUT                   = 0x40,
    D3D12_ROOT_SIGNATURE_FLAG_LOCAL_ROOT_SIGNATURE                  = 0x80,
    D3D12_ROOT_SIGNATURE_FLAG_DENY_AMPLIFICATION_SHADER_ROOT_ACCESS = 0x100,
    D3D12_ROOT_SIGNATURE_FLAG_DENY_MESH_SHADER_ROOT_ACCESS          = 0x200,
    D3D12_ROOT_SIGNATURE_FLAG_CBV_SRV_UAV_HEAP_DIRECTLY_INDEXED     = 0x400,
    D3D12_ROOT_SIGNATURE_FLAG_SAMPLER_HEAP_DIRECTLY_INDEXED         = 0x800,
} D3D12_ROOT_SIGNATURE_FLAGS;
cpp_quote( "DEFINE_ENUM_FLAG_OPERATORS( D3D12_ROOT_SIGNATURE_FLAGS )" )


typedef enum D3D12_STATIC_BORDER_COLOR
{
    D3D12_STATIC_BORDER_COLOR_TRANSPARENT_BLACK, // 0.0f,0.0f,0.0f,0.0f
    D3D12_STATIC_BORDER_COLOR_OPAQUE_BLACK, // 0.0f,0.0f,0.0f,1.0f
    D3D12_STATIC_BORDER_COLOR_OPAQUE_WHITE, // 1.0f,1.0f,1.0f,1.0f
    D3D12_STATIC_BORDER_COLOR_OPAQUE_BLACK_UINT, // 0,0,0,1
    D3D12_STATIC_BORDER_COLOR_OPAQUE_WHITE_UINT,  // 1,1,1,1

} D3D12_STATIC_BORDER_COLOR;

typedef struct D3D12_STATIC_SAMPLER_DESC
{
    D3D12_FILTER Filter;
    D3D12_TEXTURE_ADDRESS_MODE AddressU;
    D3D12_TEXTURE_ADDRESS_MODE AddressV;
    D3D12_TEXTURE_ADDRESS_MODE AddressW;
    FLOAT MipLODBias;
    UINT MaxAnisotropy;
    D3D12_COMPARISON_FUNC ComparisonFunc;
    D3D12_STATIC_BORDER_COLOR BorderColor; 
    FLOAT MinLOD;
    FLOAT MaxLOD;
    UINT ShaderRegister; 
    UINT RegisterSpace; 
    D3D12_SHADER_VISIBILITY ShaderVisibility;    
} D3D12_STATIC_SAMPLER_DESC;

typedef struct D3D12_STATIC_SAMPLER_DESC1
{
    D3D12_FILTER Filter;
    D3D12_TEXTURE_ADDRESS_MODE AddressU;
    D3D12_TEXTURE_ADDRESS_MODE AddressV;
    D3D12_TEXTURE_ADDRESS_MODE AddressW;
    FLOAT MipLODBias;
    UINT MaxAnisotropy;
    D3D12_COMPARISON_FUNC ComparisonFunc;
    D3D12_STATIC_BORDER_COLOR BorderColor;
    FLOAT MinLOD;
    FLOAT MaxLOD;
    UINT ShaderRegister;
    UINT RegisterSpace;
    D3D12_SHADER_VISIBILITY ShaderVisibility;
    D3D12_SAMPLER_FLAGS Flags;
} D3D12_STATIC_SAMPLER_DESC1;

typedef struct D3D12_ROOT_SIGNATURE_DESC
{
    UINT NumParameters;
    [annotation("_Field_size_full_(NumParameters)")] const D3D12_ROOT_PARAMETER* pParameters;
    UINT NumStaticSamplers;
    [annotation("_Field_size_full_(NumStaticSamplers)")] const D3D12_STATIC_SAMPLER_DESC* pStaticSamplers;
    D3D12_ROOT_SIGNATURE_FLAGS Flags;
} D3D12_ROOT_SIGNATURE_DESC;

typedef enum D3D12_DESCRIPTOR_RANGE_FLAGS
{
    D3D12_DESCRIPTOR_RANGE_FLAG_NONE = 0x0,
    D3D12_DESCRIPTOR_RANGE_FLAG_DESCRIPTORS_VOLATILE = 0x1,
    D3D12_DESCRIPTOR_RANGE_FLAG_DATA_VOLATILE = 0x2,
    D3D12_DESCRIPTOR_RANGE_FLAG_DATA_STATIC_WHILE_SET_AT_EXECUTE = 0x4,
    D3D12_DESCRIPTOR_RANGE_FLAG_DATA_STATIC = 0x8,
    D3D12_DESCRIPTOR_RANGE_FLAG_DESCRIPTORS_STATIC_KEEPING_BUFFER_BOUNDS_CHECKS = 0x10000,
} D3D12_DESCRIPTOR_RANGE_FLAGS;
cpp_quote("DEFINE_ENUM_FLAG_OPERATORS( D3D12_DESCRIPTOR_RANGE_FLAGS )")

typedef struct D3D12_DESCRIPTOR_RANGE1
{
    D3D12_DESCRIPTOR_RANGE_TYPE RangeType;
    UINT NumDescriptors;
    UINT BaseShaderRegister;
    UINT RegisterSpace;
    D3D12_DESCRIPTOR_RANGE_FLAGS Flags;
    UINT OffsetInDescriptorsFromTableStart;
} D3D12_DESCRIPTOR_RANGE1;

typedef struct D3D12_ROOT_DESCRIPTOR_TABLE1
{
    UINT NumDescriptorRanges;
    [annotation("_Field_size_full_(NumDescriptorRanges)")] const D3D12_DESCRIPTOR_RANGE1* pDescriptorRanges;
} D3D12_ROOT_DESCRIPTOR_TABLE1;

typedef enum D3D12_ROOT_DESCRIPTOR_FLAGS
{
    D3D12_ROOT_DESCRIPTOR_FLAG_NONE = 0x0,
    D3D12_ROOT_DESCRIPTOR_FLAG_DATA_VOLATILE = 0x2,
    D3D12_ROOT_DESCRIPTOR_FLAG_DATA_STATIC_WHILE_SET_AT_EXECUTE = 0x4,
    D3D12_ROOT_DESCRIPTOR_FLAG_DATA_STATIC = 0x8,
} D3D12_ROOT_DESCRIPTOR_FLAGS;
cpp_quote("DEFINE_ENUM_FLAG_OPERATORS( D3D12_ROOT_DESCRIPTOR_FLAGS )")

typedef struct D3D12_ROOT_DESCRIPTOR1
{
    UINT ShaderRegister;
    UINT RegisterSpace;
    D3D12_ROOT_DESCRIPTOR_FLAGS Flags;
} D3D12_ROOT_DESCRIPTOR1;

typedef struct D3D12_ROOT_PARAMETER1
{
    D3D12_ROOT_PARAMETER_TYPE ParameterType;
    union
    {
        D3D12_ROOT_DESCRIPTOR_TABLE1   DescriptorTable;
        D3D12_ROOT_CONSTANTS           Constants;
        D3D12_ROOT_DESCRIPTOR1         Descriptor;
    };
    D3D12_SHADER_VISIBILITY ShaderVisibility;
} D3D12_ROOT_PARAMETER1;

typedef struct D3D12_ROOT_SIGNATURE_DESC1
{
    UINT NumParameters;
    [annotation("_Field_size_full_(NumParameters)")] const D3D12_ROOT_PARAMETER1* pParameters;
    UINT NumStaticSamplers;
    [annotation("_Field_size_full_(NumStaticSamplers)")] const D3D12_STATIC_SAMPLER_DESC* pStaticSamplers;
    D3D12_ROOT_SIGNATURE_FLAGS Flags;
} D3D12_ROOT_SIGNATURE_DESC1;

typedef struct D3D12_ROOT_SIGNATURE_DESC2
{
    UINT NumParameters;
    [annotation("_Field_size_full_(NumParameters)")] const D3D12_ROOT_PARAMETER1* pParameters;
    UINT NumStaticSamplers;
    [annotation("_Field_size_full_(NumStaticSamplers)")] const D3D12_STATIC_SAMPLER_DESC1* pStaticSamplers;
    D3D12_ROOT_SIGNATURE_FLAGS Flags;
} D3D12_ROOT_SIGNATURE_DESC2;


typedef struct D3D12_VERSIONED_ROOT_SIGNATURE_DESC
{
    D3D_ROOT_SIGNATURE_VERSION Version;
    union
    {
        D3D12_ROOT_SIGNATURE_DESC   Desc_1_0;
        D3D12_ROOT_SIGNATURE_DESC1  Desc_1_1;
        D3D12_ROOT_SIGNATURE_DESC2  Desc_1_2;
    };
} D3D12_VERSIONED_ROOT_SIGNATURE_DESC;

[ uuid( 34AB647B-3CC8-46AC-841B-C0965645C046 ), object, local, pointer_default( unique ) ]
interface ID3D12RootSignatureDeserializer
    : IUnknown
{
    const D3D12_ROOT_SIGNATURE_DESC * GetRootSignatureDesc();
};

[ uuid( 7F91CE67-090C-4BB7-B78E-ED8FF2E31DA0 ), object, local, pointer_default( unique ) ]
interface ID3D12VersionedRootSignatureDeserializer
    : IUnknown
{    
    HRESULT GetRootSignatureDescAtVersion(D3D_ROOT_SIGNATURE_VERSION convertToVersion, [annotation("_Out_")] const D3D12_VERSIONED_ROOT_SIGNATURE_DESC** ppDesc);
    const D3D12_VERSIONED_ROOT_SIGNATURE_DESC* GetUnconvertedRootSignatureDesc();
};

cpp_quote( "typedef HRESULT (WINAPI* PFN_D3D12_SERIALIZE_ROOT_SIGNATURE)(")
cpp_quote( "                            _In_ const D3D12_ROOT_SIGNATURE_DESC* pRootSignature," )
cpp_quote( "                            _In_ D3D_ROOT_SIGNATURE_VERSION Version," )
cpp_quote( "                            _Out_ ID3DBlob** ppBlob," )
cpp_quote( "                            _Always_(_Outptr_opt_result_maybenull_) ID3DBlob** ppErrorBlob);" )
cpp_quote( "" )
cpp_quote( "HRESULT WINAPI D3D12SerializeRootSignature(")
cpp_quote( "                            _In_ const D3D12_ROOT_SIGNATURE_DESC* pRootSignature," )
cpp_quote( "                            _In_ D3D_ROOT_SIGNATURE_VERSION Version," )
cpp_quote( "                            _Out_ ID3DBlob** ppBlob," )
cpp_quote( "                            _Always_(_Outptr_opt_result_maybenull_) ID3DBlob** ppErrorBlob);" )
cpp_quote( "" )
cpp_quote( "typedef HRESULT (WINAPI* PFN_D3D12_CREATE_ROOT_SIGNATURE_DESERIALIZER)(")
cpp_quote( "                                     _In_reads_bytes_(SrcDataSizeInBytes) LPCVOID pSrcData," )
cpp_quote( "                                     _In_ SIZE_T SrcDataSizeInBytes," )
cpp_quote( "                                     _In_ REFIID pRootSignatureDeserializerInterface," )
cpp_quote( "                                     _Out_ void** ppRootSignatureDeserializer);" )
cpp_quote( "" )
cpp_quote( "HRESULT WINAPI D3D12CreateRootSignatureDeserializer(")
cpp_quote( "                                     _In_reads_bytes_(SrcDataSizeInBytes) LPCVOID pSrcData," )
cpp_quote( "                                     _In_ SIZE_T SrcDataSizeInBytes," )
cpp_quote( "                                     _In_ REFIID pRootSignatureDeserializerInterface," )
cpp_quote( "                                     _Out_ void** ppRootSignatureDeserializer);" )
cpp_quote( "" )
cpp_quote( "typedef HRESULT (WINAPI* PFN_D3D12_SERIALIZE_VERSIONED_ROOT_SIGNATURE)(" )
cpp_quote( "                            _In_ const D3D12_VERSIONED_ROOT_SIGNATURE_DESC* pRootSignature," )
cpp_quote( "                            _Out_ ID3DBlob** ppBlob," )
cpp_quote( "                            _Always_(_Outptr_opt_result_maybenull_) ID3DBlob** ppErrorBlob);" )
cpp_quote( "" )
cpp_quote( "HRESULT WINAPI D3D12SerializeVersionedRootSignature(" )
cpp_quote( "                            _In_ const D3D12_VERSIONED_ROOT_SIGNATURE_DESC* pRootSignature," )
cpp_quote( "                            _Out_ ID3DBlob** ppBlob," )
cpp_quote( "                            _Always_(_Outptr_opt_result_maybenull_) ID3DBlob** ppErrorBlob);" )
cpp_quote( "" )
cpp_quote( "typedef HRESULT (WINAPI* PFN_D3D12_CREATE_VERSIONED_ROOT_SIGNATURE_DESERIALIZER)("  )
cpp_quote( "                                     _In_reads_bytes_(SrcDataSizeInBytes) LPCVOID pSrcData," )
cpp_quote( "                                     _In_ SIZE_T SrcDataSizeInBytes," )
cpp_quote( "                                     _In_ REFIID pRootSignatureDeserializerInterface," )
cpp_quote( "                                     _Out_ void** ppRootSignatureDeserializer);" )
cpp_quote( "" )
cpp_quote( "HRESULT WINAPI D3D12CreateVersionedRootSignatureDeserializer(")
cpp_quote( "                                     _In_reads_bytes_(SrcDataSizeInBytes) LPCVOID pSrcData," )
cpp_quote( "                                     _In_ SIZE_T SrcDataSizeInBytes," )
cpp_quote( "                                     _In_ REFIID pRootSignatureDeserializerInterface," )
cpp_quote( "                                     _Out_ void** ppRootSignatureDeserializer);" )
cpp_quote( "" )



typedef struct D3D12_CPU_DESCRIPTOR_HANDLE
{
    SIZE_T ptr;
} D3D12_CPU_DESCRIPTOR_HANDLE; 


typedef struct D3D12_GPU_DESCRIPTOR_HANDLE
{
    UINT64 ptr;
} D3D12_GPU_DESCRIPTOR_HANDLE;

cpp_quote( "// If rects are supplied in D3D12_DISCARD_REGION, below, the resource " )
cpp_quote( "// must have 2D subresources with all specified subresources the same dimension." )
typedef struct D3D12_DISCARD_REGION
{
    UINT NumRects;
    [annotation("_In_reads_(NumRects)")] const D3D12_RECT* pRects;
    UINT FirstSubresource;
    UINT NumSubresources;
} D3D12_DISCARD_REGION;

typedef enum D3D12_QUERY_HEAP_TYPE
{
    D3D12_QUERY_HEAP_TYPE_OCCLUSION                            = 0,
    D3D12_QUERY_HEAP_TYPE_TIMESTAMP                            = 1,
    D3D12_QUERY_HEAP_TYPE_PIPELINE_STATISTICS                  = 2,
    D3D12_QUERY_HEAP_TYPE_SO_STATISTICS                        = 3,
    D3D12_QUERY_HEAP_TYPE_VIDEO_DECODE_STATISTICS              = 4, 
    D3D12_QUERY_HEAP_TYPE_COPY_QUEUE_TIMESTAMP                 = 5,
    D3D12_QUERY_HEAP_TYPE_PIPELINE_STATISTICS1                 = 7,

} D3D12_QUERY_HEAP_TYPE;

typedef struct D3D12_QUERY_HEAP_DESC
{
    D3D12_QUERY_HEAP_TYPE Type;
    UINT Count;
    UINT NodeMask;
} D3D12_QUERY_HEAP_DESC; 

typedef enum D3D12_QUERY_TYPE
{
    D3D12_QUERY_TYPE_OCCLUSION                      = 0,
    D3D12_QUERY_TYPE_BINARY_OCCLUSION               = 1,
    D3D12_QUERY_TYPE_TIMESTAMP                      = 2,
    D3D12_QUERY_TYPE_PIPELINE_STATISTICS            = 3,
    D3D12_QUERY_TYPE_SO_STATISTICS_STREAM0          = 4,
    D3D12_QUERY_TYPE_SO_STATISTICS_STREAM1          = 5,
    D3D12_QUERY_TYPE_SO_STATISTICS_STREAM2          = 6,
    D3D12_QUERY_TYPE_SO_STATISTICS_STREAM3          = 7,
    D3D12_QUERY_TYPE_VIDEO_DECODE_STATISTICS        = 8,
    D3D12_QUERY_TYPE_PIPELINE_STATISTICS1           = 10,

} D3D12_QUERY_TYPE;

typedef enum D3D12_PREDICATION_OP
{
    D3D12_PREDICATION_OP_EQUAL_ZERO     = 0,
    D3D12_PREDICATION_OP_NOT_EQUAL_ZERO = 1,
} D3D12_PREDICATION_OP;

typedef struct D3D12_QUERY_DATA_PIPELINE_STATISTICS
{
    UINT64 IAVertices;
    UINT64 IAPrimitives;
    UINT64 VSInvocations;
    UINT64 GSInvocations;
    UINT64 GSPrimitives;
    UINT64 CInvocations;
    UINT64 CPrimitives;
    UINT64 PSInvocations;
    UINT64 HSInvocations;
    UINT64 DSInvocations;
    UINT64 CSInvocations;
} D3D12_QUERY_DATA_PIPELINE_STATISTICS;

typedef struct D3D12_QUERY_DATA_PIPELINE_STATISTICS1
{
    UINT64 IAVertices;
    UINT64 IAPrimitives;
    UINT64 VSInvocations;
    UINT64 GSInvocations;
    UINT64 GSPrimitives;
    UINT64 CInvocations;
    UINT64 CPrimitives;
    UINT64 PSInvocations;
    UINT64 HSInvocations;
    UINT64 DSInvocations;
    UINT64 CSInvocations;
    UINT64 ASInvocations;
    UINT64 MSInvocations;
    UINT64 MSPrimitives;
} D3D12_QUERY_DATA_PIPELINE_STATISTICS1;

typedef struct D3D12_QUERY_DATA_SO_STATISTICS
{
    UINT64 NumPrimitivesWritten;
    UINT64 PrimitivesStorageNeeded;
} D3D12_QUERY_DATA_SO_STATISTICS;

typedef struct D3D12_STREAM_OUTPUT_BUFFER_VIEW
{
    D3D12_GPU_VIRTUAL_ADDRESS BufferLocation;
    UINT64 SizeInBytes;
    D3D12_GPU_VIRTUAL_ADDRESS BufferFilledSizeLocation;
} D3D12_STREAM_OUTPUT_BUFFER_VIEW;

typedef struct D3D12_DRAW_ARGUMENTS
{
    UINT VertexCountPerInstance;
    UINT InstanceCount;
    UINT StartVertexLocation;
    UINT StartInstanceLocation;
} D3D12_DRAW_ARGUMENTS;

typedef struct D3D12_DRAW_INDEXED_ARGUMENTS
{
    UINT IndexCountPerInstance;
    UINT InstanceCount;
    UINT StartIndexLocation;
    INT  BaseVertexLocation;
    UINT StartInstanceLocation;
} D3D12_DRAW_INDEXED_ARGUMENTS;

typedef struct D3D12_DISPATCH_ARGUMENTS
{
    UINT ThreadGroupCountX;
    UINT ThreadGroupCountY;
    UINT ThreadGroupCountZ;
} D3D12_DISPATCH_ARGUMENTS;

typedef struct D3D12_VERTEX_BUFFER_VIEW
{
    D3D12_GPU_VIRTUAL_ADDRESS BufferLocation;
    UINT SizeInBytes; 
    UINT StrideInBytes;
} D3D12_VERTEX_BUFFER_VIEW;

typedef struct D3D12_INDEX_BUFFER_VIEW
{
    D3D12_GPU_VIRTUAL_ADDRESS BufferLocation;
    UINT SizeInBytes; 
    DXGI_FORMAT Format;
} D3D12_INDEX_BUFFER_VIEW;

typedef enum D3D12_INDIRECT_ARGUMENT_TYPE
{
    D3D12_INDIRECT_ARGUMENT_TYPE_DRAW,
    D3D12_INDIRECT_ARGUMENT_TYPE_DRAW_INDEXED,
    D3D12_INDIRECT_ARGUMENT_TYPE_DISPATCH,
    D3D12_INDIRECT_ARGUMENT_TYPE_VERTEX_BUFFER_VIEW,
    D3D12_INDIRECT_ARGUMENT_TYPE_INDEX_BUFFER_VIEW,
    D3D12_INDIRECT_ARGUMENT_TYPE_CONSTANT,
    D3D12_INDIRECT_ARGUMENT_TYPE_CONSTANT_BUFFER_VIEW,
    D3D12_INDIRECT_ARGUMENT_TYPE_SHADER_RESOURCE_VIEW,
    D3D12_INDIRECT_ARGUMENT_TYPE_UNORDERED_ACCESS_VIEW,
    D3D12_INDIRECT_ARGUMENT_TYPE_DISPATCH_RAYS,
    D3D12_INDIRECT_ARGUMENT_TYPE_DISPATCH_MESH,
    D3D12_INDIRECT_ARGUMENT_TYPE_INCREMENTING_CONSTANT,
} D3D12_INDIRECT_ARGUMENT_TYPE;

typedef struct D3D12_INDIRECT_ARGUMENT_DESC
{
    D3D12_INDIRECT_ARGUMENT_TYPE Type;

    union
    {
        struct
        {
            UINT Slot;
        } VertexBuffer;

        struct
        {
            UINT RootParameterIndex;
            UINT DestOffsetIn32BitValues;
            UINT Num32BitValuesToSet;
        } Constant;

        struct
        {
            UINT RootParameterIndex;
        } ConstantBufferView;

        struct
        {
            UINT RootParameterIndex;
        } ShaderResourceView;

        struct
        {
            UINT RootParameterIndex;
        } UnorderedAccessView;

        // Tier 1.1 support
        struct
        {
            UINT RootParameterIndex;
            UINT DestOffsetIn32BitValues;
        } IncrementingConstant;
    };
} D3D12_INDIRECT_ARGUMENT_DESC;

typedef struct D3D12_COMMAND_SIGNATURE_DESC
{
    // The number of bytes between each drawing structure
    UINT ByteStride; 
    UINT NumArgumentDescs;
    [annotation("_Field_size_full_(NumArgumentDescs)")] const D3D12_INDIRECT_ARGUMENT_DESC* pArgumentDescs;
    UINT NodeMask;
} D3D12_COMMAND_SIGNATURE_DESC;

interface ID3D12Device;

[ uuid( c4fec28f-7966-4e95-9f94-f431cb56c3b8 ), object, local, pointer_default( unique ) ]
interface ID3D12Object
    : IUnknown
{
    HRESULT GetPrivateData(
        [annotation("_In_")] REFGUID guid,
        [annotation("_Inout_")] UINT* pDataSize,
        [annotation("_Out_writes_bytes_opt_( *pDataSize )")] void* pData );
    HRESULT SetPrivateData(
        [annotation("_In_")] REFGUID guid,
        [annotation("_In_")] UINT DataSize,
        [annotation("_In_reads_bytes_opt_( DataSize )")] const void* pData );
    HRESULT SetPrivateDataInterface(
        [annotation("_In_")] REFGUID guid,
        [annotation("_In_opt_")] const IUnknown* pData );
    HRESULT SetName(
        [annotation("_In_z_")] LPCWSTR Name );
}

[ uuid( 905db94b-a00c-4140-9df5-2b64ca9ea357 ), object, local, pointer_default( unique ) ]
interface ID3D12DeviceChild
    : ID3D12Object
{
    HRESULT GetDevice( 
        [in] REFIID riid,
        [out, iid_is(riid), annotation("_COM_Outptr_opt_")] void** ppvDevice
        );
}

[ uuid( 63ee58fb-1268-4835-86da-f008ce62f0d6 ), object, local, pointer_default( unique ) ]
interface ID3D12Pageable
    : ID3D12DeviceChild
{
}

[ uuid( 6b3b2502-6e51-45b3-90ee-9884265e8df3 ), object, local, pointer_default( unique ) ]
interface ID3D12Heap
    : ID3D12Pageable
{
    D3D12_HEAP_DESC GetDesc();
}

[ uuid( 696442be-a72e-4059-bc79-5b5c98040fad ), object, local, pointer_default( unique ) ]
interface ID3D12Resource
    : ID3D12Pageable
{
    HRESULT Map(
        UINT Subresource,
        [annotation("_In_opt_")] const D3D12_RANGE* pReadRange,
        [annotation("_Outptr_opt_result_bytebuffer_(_Inexpressible_(\"Dependent on resource\"))")] void** ppData);

    void Unmap(UINT Subresource, [annotation("_In_opt_")] const D3D12_RANGE* pWrittenRange );

    D3D12_RESOURCE_DESC GetDesc();

    D3D12_GPU_VIRTUAL_ADDRESS GetGPUVirtualAddress();

    HRESULT WriteToSubresource(
        UINT DstSubresource,
        [annotation("_In_opt_")] const D3D12_BOX* pDstBox,
        [annotation("_In_")] const void* pSrcData,
        UINT SrcRowPitch,
        UINT SrcDepthPitch );
        
    HRESULT ReadFromSubresource(
        [annotation("_Out_")] void* pDstData,
        UINT DstRowPitch,
        UINT DstDepthPitch,
        UINT SrcSubresource,
        [annotation("_In_opt_")] const D3D12_BOX* pSrcBox );

    HRESULT GetHeapProperties(
        [annotation("_Out_opt_")] D3D12_HEAP_PROPERTIES* pHeapProperties,
        [annotation("_Out_opt_")] D3D12_HEAP_FLAGS* pHeapFlags
        );
}

[ uuid( 6102dee4-af59-4b09-b999-b44d73f09b24 ), object, local, pointer_default( unique ) ]
interface ID3D12CommandAllocator
    : ID3D12Pageable
{
    HRESULT Reset();
}

[ uuid( 0a753dcf-c4d8-4b91-adf6-be5a60d95a76 ), object, local, pointer_default( unique ) ]
interface ID3D12Fence 
    : ID3D12Pageable
{
    UINT64 GetCompletedValue();
    HRESULT SetEventOnCompletion(UINT64 Value, HANDLE hEvent);
    HRESULT Signal(UINT64 Value);
};

[ uuid( 433685fe-e22b-4ca0-a8db-b5b4f4dd0e4a ), object, local, pointer_default( unique ) ]
interface ID3D12Fence1
    : ID3D12Fence
{
    D3D12_FENCE_FLAGS GetCreationFlags();
};

[ uuid( 765a30f3-f624-4c6f-a828-ace948622445 ), object, local, pointer_default( unique ) ]
interface ID3D12PipelineState
    : ID3D12Pageable
{
    HRESULT GetCachedBlob([annotation("_COM_Outptr_")] ID3DBlob** ppBlob);
}

[ uuid( 8efb471d-616c-4f49-90f7-127bb763fa51 ), object, local, pointer_default( unique ) ]
interface ID3D12DescriptorHeap
    : ID3D12Pageable
{
    D3D12_DESCRIPTOR_HEAP_DESC GetDesc();

    D3D12_CPU_DESCRIPTOR_HANDLE GetCPUDescriptorHandleForHeapStart();
    D3D12_GPU_DESCRIPTOR_HANDLE GetGPUDescriptorHandleForHeapStart();
}

[ uuid( 0d9658ae-ed45-469e-a61d-970ec583cab4 ), object, local, pointer_default( unique ) ]
interface ID3D12QueryHeap
    : ID3D12Pageable
{
};

[ uuid( c36a797c-ec80-4f0a-8985-a7b2475082d1 ), object, local, pointer_default( unique ) ]
interface ID3D12CommandSignature
    : ID3D12Pageable
{
};

[ uuid( 7116d91c-e7e4-47ce-b8c6-ec8168f437e5 ), object, local, pointer_default( unique ) ]
interface ID3D12CommandList
    : ID3D12DeviceChild
{
    D3D12_COMMAND_LIST_TYPE GetType();
}

// Superseded by ID3D12GraphicsCommandList1
[ uuid( 5b160d0f-ac1b-4185-8ba8-b3ae42a5a455 ), object, local, pointer_default( unique ) ]
interface ID3D12GraphicsCommandList
    : ID3D12CommandList
{
    HRESULT Close();

    HRESULT Reset(
        [annotation("_In_")] ID3D12CommandAllocator* pAllocator,
        [annotation("_In_opt_")] ID3D12PipelineState* pInitialState
        );

    void ClearState(
        [annotation("_In_opt_")] ID3D12PipelineState* pPipelineState
        );

    void DrawInstanced(
        [annotation("_In_")] UINT VertexCountPerInstance,
        [annotation("_In_")] UINT InstanceCount,
        [annotation("_In_")] UINT StartVertexLocation,
        [annotation("_In_")] UINT StartInstanceLocation
        );

    void DrawIndexedInstanced(
        [annotation("_In_")] UINT IndexCountPerInstance,
        [annotation("_In_")] UINT InstanceCount,
        [annotation("_In_")] UINT StartIndexLocation,
        [annotation("_In_")] INT BaseVertexLocation,
        [annotation("_In_")] UINT StartInstanceLocation
        );

    void Dispatch(
        [annotation("_In_")] UINT ThreadGroupCountX,
        [annotation("_In_")] UINT ThreadGroupCountY,
        [annotation("_In_")] UINT ThreadGroupCountZ
        );

    void CopyBufferRegion(
        [annotation("_In_")] ID3D12Resource* pDstBuffer,
        UINT64 DstOffset,
        [annotation("_In_")] ID3D12Resource* pSrcBuffer,
        UINT64 SrcOffset,
        UINT64 NumBytes
        );

    void CopyTextureRegion(
        [annotation("_In_")] const D3D12_TEXTURE_COPY_LOCATION* pDst,
        UINT DstX, UINT DstY, UINT DstZ,
        [annotation("_In_")] const D3D12_TEXTURE_COPY_LOCATION* pSrc,
        [annotation("_In_opt_")] const D3D12_BOX* pSrcBox
        );

    void CopyResource(
        [annotation("_In_")] ID3D12Resource* pDstResource,
        [annotation("_In_")] ID3D12Resource* pSrcResource
        );

    void CopyTiles(
        [annotation("_In_")] ID3D12Resource* pTiledResource,
        [annotation("_In_")] const D3D12_TILED_RESOURCE_COORDINATE* pTileRegionStartCoordinate,
        [annotation("_In_")] const D3D12_TILE_REGION_SIZE* pTileRegionSize,
        [annotation("_In_")] ID3D12Resource* pBuffer,
        UINT64 BufferStartOffsetInBytes,
        D3D12_TILE_COPY_FLAGS Flags
        );

    void ResolveSubresource(
        [annotation("_In_")] ID3D12Resource* pDstResource,
        [annotation("_In_")] UINT DstSubresource,
        [annotation("_In_")] ID3D12Resource* pSrcResource,
        [annotation("_In_")] UINT SrcSubresource,
        [annotation("_In_")] DXGI_FORMAT Format
        );

    void IASetPrimitiveTopology(
        [annotation("_In_")] D3D12_PRIMITIVE_TOPOLOGY PrimitiveTopology
        );

    void RSSetViewports(
        [annotation("_In_range_(0, D3D12_VIEWPORT_AND_SCISSORRECT_OBJECT_COUNT_PER_PIPELINE)")] UINT NumViewports,
        [annotation("_In_reads_( NumViewports)")] const D3D12_VIEWPORT* pViewports
        );

    void RSSetScissorRects(
        [annotation("_In_range_(0, D3D12_VIEWPORT_AND_SCISSORRECT_OBJECT_COUNT_PER_PIPELINE)")] UINT NumRects,
        [annotation("_In_reads_( NumRects)")] const D3D12_RECT* pRects
        );

    void OMSetBlendFactor(
        [annotation("_In_reads_opt_(4)")] const FLOAT BlendFactor[ 4 ]
        );

    void OMSetStencilRef(
        [annotation("_In_")] UINT StencilRef
        );

    void SetPipelineState(
        [annotation("_In_")] ID3D12PipelineState* pPipelineState
        );

    void ResourceBarrier(
        [annotation("_In_")] UINT NumBarriers,
        [annotation("_In_reads_(NumBarriers)")] const D3D12_RESOURCE_BARRIER* pBarriers
        );

    void ExecuteBundle(
        [annotation("_In_")] ID3D12GraphicsCommandList* pCommandList
        );

    void SetDescriptorHeaps(
        [annotation("_In_")] UINT NumDescriptorHeaps,
        [annotation("_In_reads_(NumDescriptorHeaps)")] ID3D12DescriptorHeap* const* ppDescriptorHeaps
        );

    void SetComputeRootSignature(
        [annotation("_In_opt_")] ID3D12RootSignature* pRootSignature
        );

    void SetGraphicsRootSignature(
        [annotation("_In_opt_")] ID3D12RootSignature* pRootSignature
        );

    void SetComputeRootDescriptorTable(
        [annotation("_In_")] UINT RootParameterIndex,
        [annotation("_In_")] D3D12_GPU_DESCRIPTOR_HANDLE BaseDescriptor
        );

    void SetGraphicsRootDescriptorTable(
        [annotation("_In_")] UINT RootParameterIndex,
        [annotation("_In_")] D3D12_GPU_DESCRIPTOR_HANDLE BaseDescriptor
        );

    // Single constant
    void SetComputeRoot32BitConstant(
        [annotation("_In_")] UINT RootParameterIndex,
        [annotation("_In_")] UINT SrcData,
        [annotation("_In_")] UINT DestOffsetIn32BitValues
        );

    void SetGraphicsRoot32BitConstant(
        [annotation("_In_")] UINT RootParameterIndex,
        [annotation("_In_")] UINT SrcData,
        [annotation("_In_")] UINT DestOffsetIn32BitValues
        );

    // Group of constants
    void SetComputeRoot32BitConstants(
        [annotation("_In_")] UINT RootParameterIndex,
        [annotation("_In_")] UINT Num32BitValuesToSet,
        [annotation("_In_reads_(Num32BitValuesToSet*sizeof(UINT))")] const void* pSrcData,
        [annotation("_In_")] UINT DestOffsetIn32BitValues
        );

    void SetGraphicsRoot32BitConstants(
        [annotation("_In_")] UINT RootParameterIndex,
        [annotation("_In_")] UINT Num32BitValuesToSet,
        [annotation("_In_reads_(Num32BitValuesToSet*sizeof(UINT))")] const void* pSrcData,
        [annotation("_In_")] UINT DestOffsetIn32BitValues
        );

    // CBV
    void SetComputeRootConstantBufferView(
        [annotation("_In_")] UINT RootParameterIndex,
        [annotation("_In_")] D3D12_GPU_VIRTUAL_ADDRESS BufferLocation
        );

    void SetGraphicsRootConstantBufferView(
        [annotation("_In_")] UINT RootParameterIndex,
        [annotation("_In_")] D3D12_GPU_VIRTUAL_ADDRESS BufferLocation
        );

    // SRV 
    void SetComputeRootShaderResourceView(
        [annotation("_In_")] UINT RootParameterIndex,
        [annotation("_In_")] D3D12_GPU_VIRTUAL_ADDRESS BufferLocation
        );

    void SetGraphicsRootShaderResourceView(
        [annotation("_In_")] UINT RootParameterIndex,
        [annotation("_In_")] D3D12_GPU_VIRTUAL_ADDRESS BufferLocation
        );

    // UAV 
    void SetComputeRootUnorderedAccessView(
        [annotation("_In_")] UINT RootParameterIndex,
        [annotation("_In_")] D3D12_GPU_VIRTUAL_ADDRESS BufferLocation
        );

    void SetGraphicsRootUnorderedAccessView(
        [annotation("_In_")] UINT RootParameterIndex,
        [annotation("_In_")] D3D12_GPU_VIRTUAL_ADDRESS BufferLocation
        );

    void IASetIndexBuffer(
        [annotation("_In_opt_")] const D3D12_INDEX_BUFFER_VIEW* pView
        );

    void IASetVertexBuffers(
        [annotation("_In_")] UINT StartSlot,
        [annotation("_In_")] UINT NumViews,
        [annotation("_In_reads_opt_(NumViews)")] const D3D12_VERTEX_BUFFER_VIEW* pViews
        );

    void SOSetTargets(
        [annotation("_In_")] UINT StartSlot,
        [annotation("_In_")] UINT NumViews,
        [annotation("_In_reads_opt_(NumViews)")] const D3D12_STREAM_OUTPUT_BUFFER_VIEW* pViews
        );

    void OMSetRenderTargets(
        [annotation("_In_")] UINT NumRenderTargetDescriptors,
        [annotation("_In_opt_")] const D3D12_CPU_DESCRIPTOR_HANDLE* pRenderTargetDescriptors,
        [annotation("_In_")] BOOL RTsSingleHandleToDescriptorRange,
        [annotation("_In_opt_")] const D3D12_CPU_DESCRIPTOR_HANDLE* pDepthStencilDescriptor
        );

    void ClearDepthStencilView(
        [annotation("_In_")] D3D12_CPU_DESCRIPTOR_HANDLE DepthStencilView,
        [annotation("_In_")] D3D12_CLEAR_FLAGS ClearFlags,
        [annotation("_In_")] FLOAT Depth,
        [annotation("_In_")] UINT8 Stencil,
        [annotation("_In_")] UINT NumRects,
        [annotation("_In_reads_(NumRects)")] const D3D12_RECT* pRects
        );

    void ClearRenderTargetView(
        [annotation("_In_")] D3D12_CPU_DESCRIPTOR_HANDLE RenderTargetView,
        [annotation("_In_")] const FLOAT ColorRGBA[4],
        [annotation("_In_")] UINT NumRects,
        [annotation("_In_reads_(NumRects)")] const D3D12_RECT* pRects
        );

    void ClearUnorderedAccessViewUint(
        [annotation("_In_")] D3D12_GPU_DESCRIPTOR_HANDLE ViewGPUHandleInCurrentHeap,
        [annotation("_In_")] D3D12_CPU_DESCRIPTOR_HANDLE ViewCPUHandle,
        [annotation("_In_")] ID3D12Resource* pResource,
        [annotation("_In_")] const UINT Values[4],
        [annotation("_In_")] UINT NumRects,
        [annotation("_In_reads_(NumRects)")] const D3D12_RECT* pRects
        );

    void ClearUnorderedAccessViewFloat(
        [annotation("_In_")] D3D12_GPU_DESCRIPTOR_HANDLE ViewGPUHandleInCurrentHeap,
        [annotation("_In_")] D3D12_CPU_DESCRIPTOR_HANDLE ViewCPUHandle,
        [annotation("_In_")] ID3D12Resource* pResource,
        [annotation("_In_")] const FLOAT Values[4],
        [annotation("_In_")] UINT NumRects,
        [annotation("_In_reads_(NumRects)")] const D3D12_RECT* pRects
        );

    void DiscardResource(
        [annotation("_In_")] ID3D12Resource* pResource,
        [annotation("_In_opt_")] const D3D12_DISCARD_REGION* pRegion
        );

    void BeginQuery(
        [annotation("_In_")] ID3D12QueryHeap* pQueryHeap,
        [annotation("_In_")] D3D12_QUERY_TYPE Type,
        [annotation("_In_")] UINT Index
        );

    void EndQuery(
        [annotation("_In_")] ID3D12QueryHeap* pQueryHeap,
        [annotation("_In_")] D3D12_QUERY_TYPE Type,
        [annotation("_In_")] UINT Index
        );

    void ResolveQueryData(
        [annotation("_In_")] ID3D12QueryHeap* pQueryHeap,
        [annotation("_In_")] D3D12_QUERY_TYPE Type,
        [annotation("_In_")] UINT StartIndex,
        [annotation("_In_")] UINT NumQueries,
        [annotation("_In_")] ID3D12Resource* pDestinationBuffer,
        [annotation("_In_")] UINT64 AlignedDestinationBufferOffset
        );

    void SetPredication(
        [annotation("_In_opt_")] ID3D12Resource* pBuffer,
        [annotation("_In_")] UINT64 AlignedBufferOffset,
        [annotation("_In_")] D3D12_PREDICATION_OP Operation
        );

    void SetMarker(UINT Metadata, [annotation("_In_reads_bytes_opt_(Size)")] const void* pData, UINT Size);
    void BeginEvent(UINT Metadata, [annotation("_In_reads_bytes_opt_(Size)")] const void* pData, UINT Size);
    void EndEvent();

    void ExecuteIndirect(
        [annotation("_In_")] ID3D12CommandSignature* pCommandSignature,
        [annotation("_In_")] UINT MaxCommandCount,
        [annotation("_In_")] ID3D12Resource* pArgumentBuffer,
        [annotation("_In_")] UINT64 ArgumentBufferOffset,
        [annotation("_In_opt_")] ID3D12Resource* pCountBuffer,
        [annotation("_In_")] UINT64 CountBufferOffset
        );
}

[ uuid( 553103fb-1fe7-4557-bb38-946d7d0e7ca7 ), object, local, pointer_default( unique ) ]
interface ID3D12GraphicsCommandList1
    : ID3D12GraphicsCommandList
{
    void AtomicCopyBufferUINT( 
        [annotation("_In_")] ID3D12Resource* pDstBuffer,
        UINT64 DstOffset,
        [annotation("_In_")] ID3D12Resource* pSrcBuffer,
        UINT64 SrcOffset,
        UINT Dependencies, // 0 Dependencies means only the dst buffer offset is synchronized
        [annotation("_In_reads_(Dependencies)")] ID3D12Resource*const* ppDependentResources,
        [annotation("_In_reads_(Dependencies)")] const D3D12_SUBRESOURCE_RANGE_UINT64* pDependentSubresourceRanges
        );

    // UINT64 is only valid on UMA architectures
    void AtomicCopyBufferUINT64(
        [annotation("_In_")] ID3D12Resource* pDstBuffer,
        UINT64 DstOffset,
        [annotation("_In_")] ID3D12Resource* pSrcBuffer,
        UINT64 SrcOffset,
        UINT Dependencies, // 0 Dependencies means only the dst buffer offset is synchronized
        [annotation("_In_reads_(Dependencies)")] ID3D12Resource*const* ppDependentResources,
        [annotation("_In_reads_(Dependencies)")] const D3D12_SUBRESOURCE_RANGE_UINT64* pDependentSubresourceRanges
        );

    void OMSetDepthBounds(
        [annotation("_In_")] FLOAT Min,
        [annotation("_In_")] FLOAT Max
        );

    void SetSamplePositions(
        [annotation("_In_")] UINT NumSamplesPerPixel,
        [annotation("_In_")] UINT NumPixels,
        [annotation("_In_reads_(NumSamplesPerPixel*NumPixels)")] D3D12_SAMPLE_POSITION* pSamplePositions
        );

    void ResolveSubresourceRegion(
        [annotation("_In_")] ID3D12Resource* pDstResource,
        [annotation("_In_")] UINT DstSubresource,
        [annotation("_In_")] UINT DstX,
        [annotation("_In_")] UINT DstY,
        [annotation("_In_")] ID3D12Resource* pSrcResource,
        [annotation("_In_")] UINT SrcSubresource,
        [annotation("_In_opt_")] D3D12_RECT* pSrcRect,
        [annotation("_In_")] DXGI_FORMAT Format,
        [annotation("_In_")] D3D12_RESOLVE_MODE ResolveMode
    );

    void SetViewInstanceMask(
        [annotation("_In_")] UINT Mask
    );

}

typedef struct D3D12_WRITEBUFFERIMMEDIATE_PARAMETER
{
    D3D12_GPU_VIRTUAL_ADDRESS Dest;
    UINT32 Value;
} D3D12_WRITEBUFFERIMMEDIATE_PARAMETER;

typedef enum D3D12_WRITEBUFFERIMMEDIATE_MODE
{
    D3D12_WRITEBUFFERIMMEDIATE_MODE_DEFAULT     = 0x0,
    D3D12_WRITEBUFFERIMMEDIATE_MODE_MARKER_IN    = 0x1,
    D3D12_WRITEBUFFERIMMEDIATE_MODE_MARKER_OUT   = 0x2,

} D3D12_WRITEBUFFERIMMEDIATE_MODE;

[uuid(38C3E585-FF17-412C-9150-4FC6F9D72A28), object, local, pointer_default(unique)]
interface ID3D12GraphicsCommandList2 : ID3D12GraphicsCommandList1
{
    void WriteBufferImmediate(
        UINT Count,
        [annotation("_In_reads_(Count)")] const D3D12_WRITEBUFFERIMMEDIATE_PARAMETER *pParams,
        [annotation("_In_reads_opt_(Count)")] const D3D12_WRITEBUFFERIMMEDIATE_MODE *pModes
    );
}

[ uuid( 0ec870a6-5d7e-4c22-8cfc-5baae07616ed ), object, local, pointer_default( unique ) ]
interface ID3D12CommandQueue
    : ID3D12Pageable
{
    void UpdateTileMappings(
        [annotation("_In_")] ID3D12Resource* pResource,
        UINT NumResourceRegions,
        [annotation("_In_reads_opt_(NumResourceRegions)")] const D3D12_TILED_RESOURCE_COORDINATE* pResourceRegionStartCoordinates,
        [annotation("_In_reads_opt_(NumResourceRegions)")] const D3D12_TILE_REGION_SIZE* pResourceRegionSizes,
        [annotation("_In_opt_")] ID3D12Heap* pHeap,
        UINT NumRanges,
        [annotation("_In_reads_opt_(NumRanges)")] const D3D12_TILE_RANGE_FLAGS* pRangeFlags,
        [annotation("_In_reads_opt_(NumRanges)")] const UINT* pHeapRangeStartOffsets,
        [annotation("_In_reads_opt_(NumRanges)")] const UINT* pRangeTileCounts,
        D3D12_TILE_MAPPING_FLAGS Flags );

    void CopyTileMappings(
        [annotation("_In_")] ID3D12Resource* pDstResource,
        [annotation("_In_")] const D3D12_TILED_RESOURCE_COORDINATE* pDstRegionStartCoordinate,
        [annotation("_In_")] ID3D12Resource* pSrcResource,
        [annotation("_In_")] const D3D12_TILED_RESOURCE_COORDINATE* pSrcRegionStartCoordinate,
        [annotation("_In_")] const D3D12_TILE_REGION_SIZE* pRegionSize,
        D3D12_TILE_MAPPING_FLAGS Flags );

    void ExecuteCommandLists(
        [annotation("_In_")] UINT NumCommandLists,
        [annotation("_In_reads_(NumCommandLists)")] ID3D12CommandList * const * ppCommandLists
        );

    void SetMarker(UINT Metadata, [annotation("_In_reads_bytes_opt_(Size)")] const void* pData, UINT Size);
    void BeginEvent(UINT Metadata, [annotation("_In_reads_bytes_opt_(Size)")] const void* pData, UINT Size);
    void EndEvent();

    HRESULT Signal(ID3D12Fence* pFence, UINT64 Value);
    HRESULT Wait(ID3D12Fence* pFence, UINT64 Value);

    HRESULT GetTimestampFrequency(
        [annotation("_Out_")] UINT64* pFrequency
        );

    HRESULT GetClockCalibration(
        [annotation("_Out_")] UINT64* pGpuTimestamp,
        [annotation("_Out_")] UINT64* pCpuTimestamp
        );

    D3D12_COMMAND_QUEUE_DESC GetDesc();
}

//--------------------------------------------------------------------------------------------------------
// The system LUID struct isn't defined in wtypes, so we repeat it here just
// for the MIDL compiler.
cpp_quote("#ifdef __midl")
cpp_quote("#ifndef LUID_DEFINED")
cpp_quote("#define LUID_DEFINED 1")
typedef struct __LUID {
    DWORD LowPart;
    LONG HighPart;
} LUID, *PLUID;
cpp_quote("#endif")
cpp_quote("#endif")

[ uuid( 189819f1-1db6-4b57-be54-1821339b85f7 ), object, local, pointer_default( unique ) ]
interface ID3D12Device
    : ID3D12Object
{
    UINT GetNodeCount();

    HRESULT CreateCommandQueue(
        [annotation("_In_")] const D3D12_COMMAND_QUEUE_DESC* pDesc,
        [in] REFIID riid, // Expected: ID3D12CommandQueue
        [out, iid_is(riid), annotation("_COM_Outptr_")] void** ppCommandQueue 
        );

    HRESULT CreateCommandAllocator(
        [annotation("_In_")] D3D12_COMMAND_LIST_TYPE type,
        [in] REFIID riid, // Expected: ID3D12CommandAllocator
        [out, iid_is(riid), annotation("_COM_Outptr_")] void** ppCommandAllocator
        );

    HRESULT CreateGraphicsPipelineState(
        [annotation("_In_")] const D3D12_GRAPHICS_PIPELINE_STATE_DESC* pDesc,
        [in] REFIID riid, // Expected: ID3D12PipelineState
        [out, iid_is(riid), annotation("_COM_Outptr_")] void** ppPipelineState
        );

    HRESULT CreateComputePipelineState(
        [annotation("_In_")] const D3D12_COMPUTE_PIPELINE_STATE_DESC* pDesc,
        [in] REFIID riid, // Expected: ID3D12PipelineState
        [out, iid_is(riid), annotation("_COM_Outptr_")] void** ppPipelineState
        );

    HRESULT CreateCommandList(
        [annotation("_In_")] UINT nodeMask,
        [annotation("_In_")] D3D12_COMMAND_LIST_TYPE type,
        [annotation("_In_")] ID3D12CommandAllocator* pCommandAllocator,
        [annotation("_In_opt_")] ID3D12PipelineState* pInitialState,
        [in] REFIID riid, // Expected: ID3D12CommandList
        [out, iid_is(riid), annotation("_COM_Outptr_")] void** ppCommandList
        );

    HRESULT CheckFeatureSupport(
        D3D12_FEATURE Feature,
        [annotation("_Inout_updates_bytes_(FeatureSupportDataSize)")] void* pFeatureSupportData,
        UINT FeatureSupportDataSize
        );

    HRESULT CreateDescriptorHeap(
        [annotation("_In_")] const D3D12_DESCRIPTOR_HEAP_DESC* pDescriptorHeapDesc,
        [in] REFIID riid, // Expected ID3D12DescriptorHeap
        [out, iid_is(riid), annotation("_COM_Outptr_")] void** ppvHeap);

    UINT GetDescriptorHandleIncrementSize(
        [annotation("_In_")] D3D12_DESCRIPTOR_HEAP_TYPE DescriptorHeapType);

    HRESULT CreateRootSignature(
        [annotation("_In_")] UINT nodeMask,
        [annotation("_In_reads_(blobLengthInBytes)")] const void* pBlobWithRootSignature,
        [annotation("_In_")] SIZE_T blobLengthInBytes,
        [in] REFIID riid, // Expected ID3D12RootSignature
        [out, iid_is(riid), annotation("_COM_Outptr_")] void** ppvRootSignature);

    void CreateConstantBufferView(
        [annotation("_In_opt_")] const D3D12_CONSTANT_BUFFER_VIEW_DESC* pDesc,
        [annotation("_In_")] D3D12_CPU_DESCRIPTOR_HANDLE DestDescriptor);

    void CreateShaderResourceView(
        [annotation("_In_opt_")] ID3D12Resource* pResource,
        [annotation("_In_opt_")] const D3D12_SHADER_RESOURCE_VIEW_DESC* pDesc,
        [annotation("_In_")] D3D12_CPU_DESCRIPTOR_HANDLE DestDescriptor);

    void CreateUnorderedAccessView(
        [annotation("_In_opt_")] ID3D12Resource* pResource,
        [annotation("_In_opt_")] ID3D12Resource* pCounterResource,
        [annotation("_In_opt_")] const D3D12_UNORDERED_ACCESS_VIEW_DESC* pDesc,
        [annotation("_In_")] D3D12_CPU_DESCRIPTOR_HANDLE DestDescriptor);

    void CreateRenderTargetView(
        [annotation("_In_opt_")] ID3D12Resource* pResource,
        [annotation("_In_opt_")] const D3D12_RENDER_TARGET_VIEW_DESC* pDesc,
        [annotation("_In_")] D3D12_CPU_DESCRIPTOR_HANDLE DestDescriptor);

    void CreateDepthStencilView(
        [annotation("_In_opt_")] ID3D12Resource* pResource,
        [annotation("_In_opt_")] const D3D12_DEPTH_STENCIL_VIEW_DESC* pDesc,
        [annotation("_In_")] D3D12_CPU_DESCRIPTOR_HANDLE DestDescriptor);

    void CreateSampler(
        [annotation("_In_")] const D3D12_SAMPLER_DESC* pDesc,
        [annotation("_In_")] D3D12_CPU_DESCRIPTOR_HANDLE DestDescriptor);

    void CopyDescriptors(
        [annotation("_In_")] UINT NumDestDescriptorRanges,
        [annotation("_In_reads_(NumDestDescriptorRanges)")] const D3D12_CPU_DESCRIPTOR_HANDLE* pDestDescriptorRangeStarts,
        [annotation("_In_reads_opt_(NumDestDescriptorRanges)")] const UINT* pDestDescriptorRangeSizes, // NULL means all ranges 1
        [annotation("_In_")] UINT NumSrcDescriptorRanges,
        [annotation("_In_reads_(NumSrcDescriptorRanges)")] const D3D12_CPU_DESCRIPTOR_HANDLE* pSrcDescriptorRangeStarts,
        [annotation("_In_reads_opt_(NumSrcDescriptorRanges)")] const UINT* pSrcDescriptorRangeSizes, // NULL means all ranges 1
        [annotation("_In_")] D3D12_DESCRIPTOR_HEAP_TYPE DescriptorHeapsType);

    void CopyDescriptorsSimple(
        [annotation("_In_")] UINT NumDescriptors,
        [annotation("_In_")] D3D12_CPU_DESCRIPTOR_HANDLE DestDescriptorRangeStart,
        [annotation("_In_")] D3D12_CPU_DESCRIPTOR_HANDLE SrcDescriptorRangeStart,
        [annotation("_In_")] D3D12_DESCRIPTOR_HEAP_TYPE DescriptorHeapsType);

    D3D12_RESOURCE_ALLOCATION_INFO GetResourceAllocationInfo(
        [annotation("_In_")] UINT visibleMask,
        [annotation("_In_")] UINT numResourceDescs,
        [annotation("_In_reads_(numResourceDescs)")] const D3D12_RESOURCE_DESC* pResourceDescs );

    D3D12_HEAP_PROPERTIES GetCustomHeapProperties( 
        [annotation("_In_")] UINT nodeMask,
        D3D12_HEAP_TYPE heapType 
        );

    HRESULT CreateCommittedResource(
        [annotation("_In_")] const D3D12_HEAP_PROPERTIES* pHeapProperties,
        D3D12_HEAP_FLAGS HeapFlags,
        [annotation("_In_")] const D3D12_RESOURCE_DESC* pDesc,
        D3D12_RESOURCE_STATES InitialResourceState,
        [annotation("_In_opt_")] const D3D12_CLEAR_VALUE* pOptimizedClearValue,
        [in] REFIID riidResource, // Expected: ID3D12Resource*
        [out, iid_is(riidResource), annotation("_COM_Outptr_opt_")] void** ppvResource );

    HRESULT CreateHeap(
        [annotation("_In_")] const D3D12_HEAP_DESC* pDesc,
        [in] REFIID riid, // Expected: ID3D12Heap*
        [out, iid_is(riid), annotation("_COM_Outptr_opt_")] void** ppvHeap );

    HRESULT CreatePlacedResource(
        [annotation("_In_")] ID3D12Heap* pHeap,
        UINT64 HeapOffset,
        [annotation("_In_")] const D3D12_RESOURCE_DESC* pDesc,
        D3D12_RESOURCE_STATES InitialState,
        [annotation("_In_opt_")] const D3D12_CLEAR_VALUE* pOptimizedClearValue,
        [in] REFIID riid, // Expected: ID3D12Resource*
        [out, iid_is(riid), annotation("_COM_Outptr_opt_")] void** ppvResource );

    HRESULT CreateReservedResource(
        [annotation("_In_")] const D3D12_RESOURCE_DESC* pDesc,
        D3D12_RESOURCE_STATES InitialState,
        [annotation("_In_opt_")] const D3D12_CLEAR_VALUE* pOptimizedClearValue,
        [in] REFIID riid, // Expected: ID3D12Resource*
        [out, iid_is(riid), annotation("_COM_Outptr_opt_")] void** ppvResource );
        
    HRESULT CreateSharedHandle(
        [annotation("_In_")] ID3D12DeviceChild* pObject,
        [annotation("_In_opt_")] const SECURITY_ATTRIBUTES* pAttributes,
        DWORD Access,
        [annotation("_In_opt_")] LPCWSTR Name,
        [annotation("_Out_")] HANDLE* pHandle );

    HRESULT OpenSharedHandle(
        [annotation("_In_")] HANDLE NTHandle,
        [in] REFIID riid, // Expected: ID3D12Resource*, ID3D12Heap*, or ID3D12Fence
        [out, iid_is(riid), annotation("_COM_Outptr_opt_")] void** ppvObj );

    HRESULT OpenSharedHandleByName(
        [annotation("_In_")] LPCWSTR Name,
        DWORD Access,
        [out, annotation("_Out_")] HANDLE* pNTHandle );

    HRESULT MakeResident(
        UINT NumObjects,
        [annotation("_In_reads_(NumObjects)")] ID3D12Pageable*const* ppObjects );

    HRESULT Evict(
        UINT NumObjects,
        [annotation("_In_reads_(NumObjects)")] ID3D12Pageable*const* ppObjects );

    HRESULT CreateFence(
        UINT64 InitialValue,
        D3D12_FENCE_FLAGS Flags,
        [in] REFIID riid, // Expected: ID3D12Fence
        [out, iid_is(riid), annotation("_COM_Outptr_")] void** ppFence);        

    HRESULT GetDeviceRemovedReason();

    void GetCopyableFootprints(
        [annotation("_In_")] const D3D12_RESOURCE_DESC* pResourceDesc,
        [annotation("_In_range_(0,D3D12_REQ_SUBRESOURCES)")] UINT FirstSubresource,
        [annotation("_In_range_(0,D3D12_REQ_SUBRESOURCES-FirstSubresource)")] UINT NumSubresources,
        UINT64 BaseOffset,
        [annotation("_Out_writes_opt_(NumSubresources)")] D3D12_PLACED_SUBRESOURCE_FOOTPRINT* pLayouts,
        [annotation("_Out_writes_opt_(NumSubresources)")] UINT* pNumRows,
        [annotation("_Out_writes_opt_(NumSubresources)")] UINT64* pRowSizeInBytes,
        [annotation("_Out_opt_")] UINT64* pTotalBytes );

    HRESULT CreateQueryHeap(
        [annotation("_In_")] const D3D12_QUERY_HEAP_DESC* pDesc,
        [in] REFIID riid, // Expected: ID3D12QueryHeap
        [out, iid_is(riid), annotation("_COM_Outptr_opt_")] void** ppvHeap
        );

    HRESULT SetStablePowerState(BOOL Enable);

    HRESULT CreateCommandSignature(
        [annotation("_In_")] const D3D12_COMMAND_SIGNATURE_DESC* pDesc,
        [annotation("_In_opt_")] ID3D12RootSignature* pRootSignature,
        [in] REFIID riid, // Expected: ID3D12CommandSignature
        [out, iid_is(riid), annotation("_COM_Outptr_opt_")] void** ppvCommandSignature
        );

    void GetResourceTiling( 
        [annotation("_In_")] ID3D12Resource* pTiledResource,
        [annotation("_Out_opt_")] UINT* pNumTilesForEntireResource, 
        [annotation("_Out_opt_")] D3D12_PACKED_MIP_INFO* pPackedMipDesc, 
        [annotation("_Out_opt_")] D3D12_TILE_SHAPE* pStandardTileShapeForNonPackedMips, 
        [annotation("_Inout_opt_")] UINT* pNumSubresourceTilings, 
        [annotation("_In_")] UINT FirstSubresourceTilingToGet, 
        [annotation("_Out_writes_(*pNumSubresourceTilings)")] D3D12_SUBRESOURCE_TILING* pSubresourceTilingsForNonPackedMips
        );

    LUID GetAdapterLuid();
}

// D3D12 Revision 1
[ uuid( c64226a8-9201-46af-b4cc-53fb9ff7414f ), object, local, pointer_default( unique ) ]
interface ID3D12PipelineLibrary
    : ID3D12DeviceChild
{
    HRESULT StorePipeline(
        [annotation("_In_opt_")] LPCWSTR pName,
        [annotation("_In_")] ID3D12PipelineState *pPipeline
        );

    HRESULT LoadGraphicsPipeline(
        [annotation("_In_")] LPCWSTR pName,
        [annotation("_In_")] const D3D12_GRAPHICS_PIPELINE_STATE_DESC* pDesc,
        [in] REFIID riid, // Expected: ID3D12PipelineState
        [out, iid_is(riid), annotation("_COM_Outptr_")] void** ppPipelineState
        );

    HRESULT LoadComputePipeline(
        [annotation("_In_")] LPCWSTR pName,
        [annotation("_In_")] const D3D12_COMPUTE_PIPELINE_STATE_DESC* pDesc,
        [in] REFIID riid, // Expected: ID3D12PipelineState
        [out, iid_is(riid), annotation("_COM_Outptr_")] void** ppPipelineState
        );       

    SIZE_T GetSerializedSize();

    HRESULT Serialize(
        [annotation("_Out_writes_(DataSizeInBytes)")] void* pData,
        SIZE_T DataSizeInBytes
        );
}

// Pipeline State v2
[ uuid( 80eabf42-2568-4e5e-bd82-c37f86961dc3 ), object, local, pointer_default( unique ) ]
interface ID3D12PipelineLibrary1
    : ID3D12PipelineLibrary
{
    HRESULT LoadPipeline(
        [annotation("_In_")] LPCWSTR pName,
        [annotation("_In_")] const D3D12_PIPELINE_STATE_STREAM_DESC* pDesc,
        [in] REFIID riid, // Expected: ID3D12PipelineState
        [out, iid_is(riid), annotation("_COM_Outptr_")] void** ppPipelineState
        );
}

typedef enum D3D12_MULTIPLE_FENCE_WAIT_FLAGS
{
    D3D12_MULTIPLE_FENCE_WAIT_FLAG_NONE = 0x0,
    D3D12_MULTIPLE_FENCE_WAIT_FLAG_ANY = 0x1,

    D3D12_MULTIPLE_FENCE_WAIT_FLAG_ALL = 0x0, // Alias, default behavior is ALL
} D3D12_MULTIPLE_FENCE_WAIT_FLAGS;
cpp_quote( "DEFINE_ENUM_FLAG_OPERATORS( D3D12_MULTIPLE_FENCE_WAIT_FLAGS )" )

// Applications may use the whole value range of UINT for a priority.
// These names are merely semantic suggestions.
typedef enum D3D12_RESIDENCY_PRIORITY
{
    D3D12_RESIDENCY_PRIORITY_MINIMUM = 0x28000000,
    D3D12_RESIDENCY_PRIORITY_LOW = 0x50000000,
    D3D12_RESIDENCY_PRIORITY_NORMAL = 0x78000000,
    D3D12_RESIDENCY_PRIORITY_HIGH = 0xa0010000,
    D3D12_RESIDENCY_PRIORITY_MAXIMUM = 0xc8000000,
} D3D12_RESIDENCY_PRIORITY;

[uuid(77acce80-638e-4e65-8895-c1f23386863e), object, local, pointer_default(unique)]
interface ID3D12Device1
    : ID3D12Device
{
    HRESULT CreatePipelineLibrary(
        [annotation("_In_reads_(BlobLength)")] const void *pLibraryBlob,
        SIZE_T BlobLength,
        [in] REFIID riid, // Expected: ID3D12PipelineLibrary
        [out, iid_is(riid), annotation("_COM_Outptr_")] void** ppPipelineLibrary
        );

    HRESULT SetEventOnMultipleFenceCompletion(
        [annotation("_In_reads_(NumFences)")] ID3D12Fence* const* ppFences,
        [annotation("_In_reads_(NumFences)")] const UINT64* pFenceValues,
        UINT NumFences,
        D3D12_MULTIPLE_FENCE_WAIT_FLAGS Flags,
        HANDLE hEvent
        );

    HRESULT SetResidencyPriority(
        UINT NumObjects,
        [annotation("_In_reads_(NumObjects)")] ID3D12Pageable*const* ppObjects,
        [annotation("_In_reads_(NumObjects)")] const D3D12_RESIDENCY_PRIORITY* pPriorities );
}

[uuid(30baa41e-b15b-475c-a0bb-1af5c5b64328), object, local, pointer_default(unique)]
interface ID3D12Device2
    : ID3D12Device1
{
    HRESULT CreatePipelineState(
        [in] const D3D12_PIPELINE_STATE_STREAM_DESC* pDesc,
        [in] REFIID riid, // Can be any pipeline state interface, provided the pipeline subobjects match
        [out, iid_is(riid), annotation("_COM_Outptr_")] void** ppPipelineState
        );
}

typedef enum D3D12_RESIDENCY_FLAGS
{
    D3D12_RESIDENCY_FLAG_NONE = 0x0,
    D3D12_RESIDENCY_FLAG_DENY_OVERBUDGET = 0x1,
} D3D12_RESIDENCY_FLAGS;
cpp_quote( "DEFINE_ENUM_FLAG_OPERATORS( D3D12_RESIDENCY_FLAGS )" )

[uuid(81dadc15-2bad-4392-93c5-101345c4aa98), object, local, pointer_default(unique)]
interface ID3D12Device3
    : ID3D12Device2
{
    HRESULT OpenExistingHeapFromAddress(
        [annotation("_In_")] const void* pAddress,
        [in] REFIID riid, // Expected: ID3D12Heap
        [out, iid_is(riid), annotation("_COM_Outptr_")] void** ppvHeap
    );
    HRESULT OpenExistingHeapFromFileMapping(
        [annotation("_In_")] HANDLE hFileMapping,
        [in] REFIID riid, // Expected: ID3D12Heap
        [out, iid_is(riid), annotation("_COM_Outptr_")] void** ppvHeap
    );
    HRESULT EnqueueMakeResident(
        D3D12_RESIDENCY_FLAGS Flags,
        UINT NumObjects,
        [annotation("_In_reads_(NumObjects)")] ID3D12Pageable*const* ppObjects,
        [annotation("_In_")] ID3D12Fence* pFenceToSignal,
        UINT64 FenceValueToSignal
    );
}

typedef enum D3D12_COMMAND_LIST_FLAGS
{
    D3D12_COMMAND_LIST_FLAG_NONE = 0x0,
} D3D12_COMMAND_LIST_FLAGS;
cpp_quote("DEFINE_ENUM_FLAG_OPERATORS( D3D12_COMMAND_LIST_FLAGS )")


typedef enum D3D12_COMMAND_POOL_FLAGS
{
    D3D12_COMMAND_POOL_FLAG_NONE = 0x0,

} D3D12_COMMAND_POOL_FLAGS;
cpp_quote("DEFINE_ENUM_FLAG_OPERATORS( D3D12_COMMAND_POOL_FLAGS )")

typedef enum D3D12_COMMAND_RECORDER_FLAGS
{
    D3D12_COMMAND_RECORDER_FLAG_NONE = 0x0,

} D3D12_COMMAND_RECORDER_FLAGS;
cpp_quote("DEFINE_ENUM_FLAG_OPERATORS( D3D12_COMMAND_RECORDER_FLAGS )")

typedef enum D3D12_PROTECTED_SESSION_STATUS
{
    D3D12_PROTECTED_SESSION_STATUS_OK = 0,
    D3D12_PROTECTED_SESSION_STATUS_INVALID = 1,
} D3D12_PROTECTED_SESSION_STATUS;

[uuid(A1533D18-0AC1-4084-85B9-89A96116806B), object, local, pointer_default(unique)]
interface ID3D12ProtectedSession
    : ID3D12DeviceChild
{
    HRESULT GetStatusFence(
        [in] REFIID riid,
        [out, iid_is(riid), annotation("_COM_Outptr_opt_")] void** ppFence);

    D3D12_PROTECTED_SESSION_STATUS GetSessionStatus();
}

typedef enum D3D12_PROTECTED_RESOURCE_SESSION_SUPPORT_FLAGS
{
    D3D12_PROTECTED_RESOURCE_SESSION_SUPPORT_FLAG_NONE = 0x0,
    D3D12_PROTECTED_RESOURCE_SESSION_SUPPORT_FLAG_SUPPORTED = 0x1,

} D3D12_PROTECTED_RESOURCE_SESSION_SUPPORT_FLAGS;
cpp_quote("DEFINE_ENUM_FLAG_OPERATORS( D3D12_PROTECTED_RESOURCE_SESSION_SUPPORT_FLAGS )")

typedef struct D3D12_FEATURE_DATA_PROTECTED_RESOURCE_SESSION_SUPPORT
{
    UINT                                            NodeIndex;  // input
    D3D12_PROTECTED_RESOURCE_SESSION_SUPPORT_FLAGS  Support;    // output
} D3D12_FEATURE_DATA_PROTECTED_RESOURCE_SESSION_SUPPORT;

typedef enum D3D12_PROTECTED_RESOURCE_SESSION_FLAGS
{
    D3D12_PROTECTED_RESOURCE_SESSION_FLAG_NONE = 0x0,

} D3D12_PROTECTED_RESOURCE_SESSION_FLAGS;
cpp_quote("DEFINE_ENUM_FLAG_OPERATORS( D3D12_PROTECTED_RESOURCE_SESSION_FLAGS )")

typedef struct D3D12_PROTECTED_RESOURCE_SESSION_DESC
{
    UINT NodeMask;
    D3D12_PROTECTED_RESOURCE_SESSION_FLAGS Flags;
} D3D12_PROTECTED_RESOURCE_SESSION_DESC;

[uuid(6CD696F4-F289-40CC-8091-5A6C0A099C3D), object, local, pointer_default(unique)]
interface ID3D12ProtectedResourceSession
    : ID3D12ProtectedSession
{
    D3D12_PROTECTED_RESOURCE_SESSION_DESC GetDesc();
}

[uuid(e865df17-a9ee-46f9-a463-3098315aa2e5), object, local, pointer_default(unique)]
interface ID3D12Device4
    : ID3D12Device3
{
    HRESULT CreateCommandList1(
        [annotation("_In_")] UINT nodeMask,
        [annotation("_In_")] D3D12_COMMAND_LIST_TYPE type,
        [annotation("_In_")] D3D12_COMMAND_LIST_FLAGS flags,
        [in] REFIID riid, // Expected: ID3D12CommandList
        [out, iid_is(riid), annotation("_COM_Outptr_")] void** ppCommandList
    );

    HRESULT CreateProtectedResourceSession(
        [annotation("_In_")] const D3D12_PROTECTED_RESOURCE_SESSION_DESC* pDesc,
        [annotation("_In_")] REFIID riid, // Expected: ID3D12ProtectedResourceSession, 
        [out, iid_is(riid), annotation("_COM_Outptr_")] void** ppSession
    );

    HRESULT CreateCommittedResource1(
        [annotation("_In_")] const D3D12_HEAP_PROPERTIES* pHeapProperties,
        D3D12_HEAP_FLAGS HeapFlags,
        [annotation("_In_")] const D3D12_RESOURCE_DESC* pDesc,
        D3D12_RESOURCE_STATES InitialResourceState,
        [annotation("_In_opt_")] const D3D12_CLEAR_VALUE* pOptimizedClearValue,
        [annotation("_In_opt_")] ID3D12ProtectedResourceSession *pProtectedSession,
        [in] REFIID riidResource, // Expected: ID3D12Resource1*
        [out, iid_is(riidResource), annotation("_COM_Outptr_opt_")] void** ppvResource
    );

    HRESULT CreateHeap1(
        [annotation("_In_")] const D3D12_HEAP_DESC* pDesc,
        [annotation("_In_opt_")] ID3D12ProtectedResourceSession *pProtectedSession,
        [in] REFIID riid, // Expected: ID3D12Heap1*
        [out, iid_is(riid), annotation("_COM_Outptr_opt_")] void** ppvHeap
    );

    HRESULT CreateReservedResource1(
        [annotation("_In_")] const D3D12_RESOURCE_DESC* pDesc,
        D3D12_RESOURCE_STATES InitialState,
        [annotation("_In_opt_")] const D3D12_CLEAR_VALUE* pOptimizedClearValue,
        [annotation("_In_opt_")] ID3D12ProtectedResourceSession *pProtectedSession,
        [in] REFIID riid, // Expected: ID3D12Resource1*
        [out, iid_is(riid), annotation("_COM_Outptr_opt_")] void** ppvResource
    );

    D3D12_RESOURCE_ALLOCATION_INFO GetResourceAllocationInfo1(
        UINT visibleMask,
        UINT numResourceDescs,
        [annotation("_In_reads_(numResourceDescs)")] const D3D12_RESOURCE_DESC* pResourceDescs,
        [annotation("_Out_writes_opt_(numResourceDescs)")] D3D12_RESOURCE_ALLOCATION_INFO1* pResourceAllocationInfo1
    );
}

typedef enum D3D12_LIFETIME_STATE
{
    D3D12_LIFETIME_STATE_IN_USE,
    D3D12_LIFETIME_STATE_NOT_IN_USE,
} D3D12_LIFETIME_STATE;

interface ID3D12LifetimeTracker;

[uuid(e667af9f-cd56-4f46-83ce-032e595d70a8), object, local, pointer_default(unique)]
interface ID3D12LifetimeOwner : IUnknown
{
    void LifetimeStateUpdated(D3D12_LIFETIME_STATE NewState);
}

[uuid(f1df64b6-57fd-49cd-8807-c0eb88b45c8f), object, local, pointer_default(unique)]
interface ID3D12SwapChainAssistant
    : IUnknown
{
    LUID GetLUID();
    HRESULT GetSwapChainObject(REFIID riid, [out, iid_is(riid), annotation("_COM_Outptr_")] void** ppv);
    HRESULT GetCurrentResourceAndCommandQueue(REFIID riidResource, [out, iid_is(riidResource), annotation("_COM_Outptr_")] void** ppvResource,
                                              REFIID riidQueue, [out, iid_is(riidQueue), annotation("_COM_Outptr_")] void** ppvQueue);
    HRESULT InsertImplicitSync();
}

[uuid(3fd03d36-4eb1-424a-a582-494ecb8ba813), object, local, pointer_default(unique)]
interface ID3D12LifetimeTracker
    : ID3D12DeviceChild
{
    HRESULT DestroyOwnedObject([annotation("_In_")] ID3D12DeviceChild* pObject);
}

typedef enum D3D12_META_COMMAND_PARAMETER_TYPE
{
    D3D12_META_COMMAND_PARAMETER_TYPE_FLOAT = 0,
    D3D12_META_COMMAND_PARAMETER_TYPE_UINT64 = 1,
    D3D12_META_COMMAND_PARAMETER_TYPE_GPU_VIRTUAL_ADDRESS = 2,
    D3D12_META_COMMAND_PARAMETER_TYPE_CPU_DESCRIPTOR_HANDLE_HEAP_TYPE_CBV_SRV_UAV = 3,
    D3D12_META_COMMAND_PARAMETER_TYPE_GPU_DESCRIPTOR_HANDLE_HEAP_TYPE_CBV_SRV_UAV = 4,
} D3D12_META_COMMAND_PARAMETER_TYPE;

typedef enum D3D12_META_COMMAND_PARAMETER_FLAGS
{
    D3D12_META_COMMAND_PARAMETER_FLAG_INPUT = 0x00000001,
    D3D12_META_COMMAND_PARAMETER_FLAG_OUTPUT = 0x00000002
} D3D12_META_COMMAND_PARAMETER_FLAGS;
cpp_quote("DEFINE_ENUM_FLAG_OPERATORS( D3D12_META_COMMAND_PARAMETER_FLAGS )")

typedef enum D3D12_META_COMMAND_PARAMETER_STAGE
{
    D3D12_META_COMMAND_PARAMETER_STAGE_CREATION = 0,
    D3D12_META_COMMAND_PARAMETER_STAGE_INITIALIZATION = 1,
    D3D12_META_COMMAND_PARAMETER_STAGE_EXECUTION = 2,
} D3D12_META_COMMAND_PARAMETER_STAGE;

typedef struct D3D12_META_COMMAND_PARAMETER_DESC
{
    LPCWSTR Name;
    D3D12_META_COMMAND_PARAMETER_TYPE Type;
    D3D12_META_COMMAND_PARAMETER_FLAGS Flags;
    D3D12_RESOURCE_STATES RequiredResourceState;
    UINT StructureOffset;
} D3D12_META_COMMAND_PARAMETER_DESC;

typedef enum D3D12_GRAPHICS_STATES
{
    D3D12_GRAPHICS_STATE_NONE = 0,
    D3D12_GRAPHICS_STATE_IA_VERTEX_BUFFERS = (1 << 0),
    D3D12_GRAPHICS_STATE_IA_INDEX_BUFFER = (1 << 1),
    D3D12_GRAPHICS_STATE_IA_PRIMITIVE_TOPOLOGY = (1 << 2),
    D3D12_GRAPHICS_STATE_DESCRIPTOR_HEAP = (1 << 3),
    D3D12_GRAPHICS_STATE_GRAPHICS_ROOT_SIGNATURE = (1 << 4),
    D3D12_GRAPHICS_STATE_COMPUTE_ROOT_SIGNATURE = (1 << 5),
    D3D12_GRAPHICS_STATE_RS_VIEWPORTS = (1 << 6),
    D3D12_GRAPHICS_STATE_RS_SCISSOR_RECTS = (1 << 7),
    D3D12_GRAPHICS_STATE_PREDICATION = (1 << 8),
    D3D12_GRAPHICS_STATE_OM_RENDER_TARGETS = (1 << 9),
    D3D12_GRAPHICS_STATE_OM_STENCIL_REF = (1 << 10),
    D3D12_GRAPHICS_STATE_OM_BLEND_FACTOR = (1 << 11),
    D3D12_GRAPHICS_STATE_PIPELINE_STATE = (1 << 12),
    D3D12_GRAPHICS_STATE_SO_TARGETS = (1 << 13),
    D3D12_GRAPHICS_STATE_OM_DEPTH_BOUNDS = (1 << 14),
    D3D12_GRAPHICS_STATE_SAMPLE_POSITIONS = (1 << 15),
    D3D12_GRAPHICS_STATE_VIEW_INSTANCE_MASK = (1 << 16),

} D3D12_GRAPHICS_STATES;
cpp_quote("DEFINE_ENUM_FLAG_OPERATORS( D3D12_GRAPHICS_STATES )")

typedef struct D3D12_META_COMMAND_DESC
{
    GUID Id;
    LPCWSTR Name;
    D3D12_GRAPHICS_STATES InitializationDirtyState;
    D3D12_GRAPHICS_STATES ExecutionDirtyState;
} D3D12_META_COMMAND_DESC;

[uuid(47016943-fca8-4594-93ea-af258b55346d), object, local, pointer_default(unique)]
interface ID3D12StateObject
    : ID3D12Pageable
{
}

[uuid(de5fa827-9bf9-4f26-89ff-d7f56fde3860), object, local, pointer_default(unique)]
interface ID3D12StateObjectProperties
    : IUnknown
{
    void* GetShaderIdentifier([annotation("_In_")] LPCWSTR pExportName);
    UINT64 GetShaderStackSize([annotation("_In_")] LPCWSTR pExportName);
    UINT64 GetPipelineStackSize();
    void SetPipelineStackSize(UINT64 PipelineStackSizeInBytes);
}

typedef struct D3D12_PROGRAM_IDENTIFIER
{
    UINT64 OpaqueData[4];
} D3D12_PROGRAM_IDENTIFIER;

[uuid(460caac7-1d24-446a-a184-ca67db494138), object, local, pointer_default(unique)]
interface ID3D12StateObjectProperties1
    : ID3D12StateObjectProperties
{
    D3D12_PROGRAM_IDENTIFIER GetProgramIdentifier(LPCWSTR pProgramName);
}

typedef struct D3D12_NODE_ID
{
    LPCWSTR Name;
    UINT    ArrayIndex;
} D3D12_NODE_ID;

typedef struct D3D12_WORK_GRAPH_MEMORY_REQUIREMENTS
{
    UINT64 MinSizeInBytes;
    UINT64 MaxSizeInBytes;
    UINT SizeGranularityInBytes;
} D3D12_WORK_GRAPH_MEMORY_REQUIREMENTS;

[uuid(065acf71-f863-4b89-82f4-02e4d5886757), object, local, pointer_default(unique)]
interface ID3D12WorkGraphProperties : IUnknown
{
    UINT GetNumWorkGraphs();
    LPCWSTR GetProgramName(UINT WorkGraphIndex);
    UINT GetWorkGraphIndex(LPCWSTR pProgramName);

    UINT GetNumNodes(UINT WorkGraphIndex);
    D3D12_NODE_ID GetNodeID(UINT WorkGraphIndex, UINT NodeIndex);
    UINT GetNodeIndex(UINT WorkGraphIndex, D3D12_NODE_ID NodeID);
    UINT GetNodeLocalRootArgumentsTableIndex(UINT WorkGraphIndex, UINT NodeIndex);

    UINT GetNumEntrypoints(UINT WorkGraphIndex);
    D3D12_NODE_ID GetEntrypointID(UINT WorkGraphIndex, UINT EntrypointIndex);
    UINT GetEntrypointIndex(UINT WorkGraphIndex, D3D12_NODE_ID NodeID);
    UINT GetEntrypointRecordSizeInBytes(UINT WorkGraphIndex, UINT EntrypointIndex);

    void GetWorkGraphMemoryRequirements(
        UINT WorkGraphIndex,
        [annotation("_Out_")] D3D12_WORK_GRAPH_MEMORY_REQUIREMENTS* pWorkGraphMemoryRequirements);

    UINT GetEntrypointRecordAlignmentInBytes(UINT WorkGraphIndex, UINT EntrypointIndex);
}

typedef enum D3D12_STATE_SUBOBJECT_TYPE
{
    D3D12_STATE_SUBOBJECT_TYPE_STATE_OBJECT_CONFIG = 0, // D3D12_STATE_OBJECT_CONFIG
    D3D12_STATE_SUBOBJECT_TYPE_GLOBAL_ROOT_SIGNATURE = 1, // D3D12_GLOBAL_ROOT_SIGNATURE
    D3D12_STATE_SUBOBJECT_TYPE_LOCAL_ROOT_SIGNATURE = 2, // D3D12_LOCAL_ROOT_SIGNATURE
    D3D12_STATE_SUBOBJECT_TYPE_NODE_MASK = 3, // D3D12_NODE_MASK
    // 4 unused
    D3D12_STATE_SUBOBJECT_TYPE_DXIL_LIBRARY = 5, // D3D12_DXIL_LIBRARY_DESC
    D3D12_STATE_SUBOBJECT_TYPE_EXISTING_COLLECTION = 6, // D3D12_EXISTING_COLLECTION_DESC
    D3D12_STATE_SUBOBJECT_TYPE_SUBOBJECT_TO_EXPORTS_ASSOCIATION = 7, // D3D12_SUBOBJECT_TO_EXPORTS_ASSOCIATION
    D3D12_STATE_SUBOBJECT_TYPE_DXIL_SUBOBJECT_TO_EXPORTS_ASSOCIATION = 8, // D3D12_DXIL_SUBOBJECT_TO_EXPORTS_ASSOCIATION
    D3D12_STATE_SUBOBJECT_TYPE_RAYTRACING_SHADER_CONFIG = 9, // D3D12_RAYTRACING_SHADER_CONFIG
    D3D12_STATE_SUBOBJECT_TYPE_RAYTRACING_PIPELINE_CONFIG = 10,  // D3D12_RAYTRACING_PIPELINE_CONFIG
    D3D12_STATE_SUBOBJECT_TYPE_HIT_GROUP = 11,  // D3D12_HIT_GROUP_DESC
    D3D12_STATE_SUBOBJECT_TYPE_RAYTRACING_PIPELINE_CONFIG1 = 12,  // D3D12_RAYTRACING_PIPELINE_CONFIG1
    D3D12_STATE_SUBOBJECT_TYPE_WORK_GRAPH = 13, // D3D12_WORK_GRAPH_DESC
    D3D12_STATE_SUBOBJECT_TYPE_STREAM_OUTPUT = 14,  // D3D12_STREAM_OUTPUT_DESC
    D3D12_STATE_SUBOBJECT_TYPE_BLEND = 15,  // D3D12_BLEND_DESC
    D3D12_STATE_SUBOBJECT_TYPE_SAMPLE_MASK = 16,  // UINT
    D3D12_STATE_SUBOBJECT_TYPE_RASTERIZER = 17,  // D3D12_RASTERIZER_DESC2
    D3D12_STATE_SUBOBJECT_TYPE_DEPTH_STENCIL = 18,  // D3D12_DEPTH_STENCIL_DESC
    D3D12_STATE_SUBOBJECT_TYPE_INPUT_LAYOUT = 19,  // D3D12_INPUT_LAYOUT_DESC
    D3D12_STATE_SUBOBJECT_TYPE_IB_STRIP_CUT_VALUE = 20,  // D3D12_INDEX_BUFFER_STRIP_CUT_VALUE
    D3D12_STATE_SUBOBJECT_TYPE_PRIMITIVE_TOPOLOGY = 21,  // D3D12_PRIMITIVE_TOPOLOGY_TYPE
    D3D12_STATE_SUBOBJECT_TYPE_RENDER_TARGET_FORMATS = 22,  // D3D12_RT_FORMAT_ARRAY
    D3D12_STATE_SUBOBJECT_TYPE_DEPTH_STENCIL_FORMAT = 23,  // DXGI_FORMAT
    D3D12_STATE_SUBOBJECT_TYPE_SAMPLE_DESC = 24,  // DXGI_SAMPLE_DESC
    // 25 is unused
    D3D12_STATE_SUBOBJECT_TYPE_FLAGS = 26,  // D3D12_PIPELINE_STATE_FLAGS
    D3D12_STATE_SUBOBJECT_TYPE_DEPTH_STENCIL1 = 27, // D3D12_DEPTH_STENCIL_DESC1
    D3D12_STATE_SUBOBJECT_TYPE_VIEW_INSTANCING = 28, // D3D12_VIEW_INSTANCING_DESC
    D3D12_STATE_SUBOBJECT_TYPE_GENERIC_PROGRAM = 29, // D3D12_GENERIC_PROGRAM_DESC
    D3D12_STATE_SUBOBJECT_TYPE_DEPTH_STENCIL2 = 30, // D3D12_DEPTH_STENCIL_DESC2

    D3D12_STATE_SUBOBJECT_TYPE_MAX_VALID,
} D3D12_STATE_SUBOBJECT_TYPE;

typedef struct D3D12_STATE_SUBOBJECT
{
    D3D12_STATE_SUBOBJECT_TYPE Type;
    const void* pDesc;
} D3D12_STATE_SUBOBJECT;

typedef enum D3D12_STATE_OBJECT_FLAGS
{
    D3D12_STATE_OBJECT_FLAG_NONE = 0x0,
    D3D12_STATE_OBJECT_FLAG_ALLOW_LOCAL_DEPENDENCIES_ON_EXTERNAL_DEFINITIONS = 0x1,
    D3D12_STATE_OBJECT_FLAG_ALLOW_EXTERNAL_DEPENDENCIES_ON_LOCAL_DEFINITIONS = 0x2,
    D3D12_STATE_OBJECT_FLAG_ALLOW_STATE_OBJECT_ADDITIONS = 0x4,
} D3D12_STATE_OBJECT_FLAGS;
cpp_quote("DEFINE_ENUM_FLAG_OPERATORS( D3D12_STATE_OBJECT_FLAGS )")

typedef struct D3D12_STATE_OBJECT_CONFIG
{
    D3D12_STATE_OBJECT_FLAGS Flags;
} D3D12_STATE_OBJECT_CONFIG;

typedef struct D3D12_GLOBAL_ROOT_SIGNATURE
{
    ID3D12RootSignature* pGlobalRootSignature;
} D3D12_GLOBAL_ROOT_SIGNATURE;

typedef struct D3D12_LOCAL_ROOT_SIGNATURE
{
    ID3D12RootSignature* pLocalRootSignature;
} D3D12_LOCAL_ROOT_SIGNATURE;

typedef struct D3D12_NODE_MASK
{
    UINT NodeMask;
} D3D12_NODE_MASK;

typedef struct D3D12_SAMPLE_MASK
{
    UINT SampleMask;
} D3D12_SAMPLE_MASK;

typedef struct D3D12_IB_STRIP_CUT_VALUE
{
    D3D12_INDEX_BUFFER_STRIP_CUT_VALUE IndexBufferStripCutValue;
} D3D12_IB_STRIP_CUT_VALUE;

typedef struct D3D12_PRIMITIVE_TOPOLOGY_DESC
{
    D3D12_PRIMITIVE_TOPOLOGY_TYPE PrimitiveTopology;
} D3D12_PRIMITIVE_TOPOLOGY_DESC;

typedef struct D3D12_DEPTH_STENCIL_FORMAT
{
    DXGI_FORMAT DepthStencilFormat;
} D3D12_DEPTH_STENCIL_FORMAT;

typedef enum D3D12_EXPORT_FLAGS
{
    D3D12_EXPORT_FLAG_NONE = 0x0,
} D3D12_EXPORT_FLAGS;
cpp_quote("DEFINE_ENUM_FLAG_OPERATORS( D3D12_EXPORT_FLAGS )")

typedef struct D3D12_EXPORT_DESC
{
    LPCWSTR Name;
    [annotation("_In_opt_")] LPCWSTR ExportToRename;
    D3D12_EXPORT_FLAGS Flags;
} D3D12_EXPORT_DESC;

typedef struct D3D12_DXIL_LIBRARY_DESC
{
    D3D12_SHADER_BYTECODE  DXILLibrary;
    UINT NumExports; // Optional, if 0 all exports in the library/collection will be surfaced
    [annotation("_In_reads_(NumExports)")] const D3D12_EXPORT_DESC* pExports;
} D3D12_DXIL_LIBRARY_DESC;

typedef struct D3D12_EXISTING_COLLECTION_DESC
{
    ID3D12StateObject* pExistingCollection;
    UINT NumExports; // Optional, if 0 all exports in the library/collection will be surfaced
    [annotation("_In_reads_(NumExports)")] const D3D12_EXPORT_DESC* pExports;
} D3D12_EXISTING_COLLECTION_DESC;

typedef struct D3D12_SUBOBJECT_TO_EXPORTS_ASSOCIATION
{
    const D3D12_STATE_SUBOBJECT* pSubobjectToAssociate; 
    UINT NumExports;                      
    [annotation("_In_reads_(NumExports)")] LPCWSTR* pExports;
} D3D12_SUBOBJECT_TO_EXPORTS_ASSOCIATION;

typedef struct D3D12_DXIL_SUBOBJECT_TO_EXPORTS_ASSOCIATION
{
    LPCWSTR SubobjectToAssociate; 
    UINT NumExports;     
    [annotation("_In_reads_(NumExports)")] LPCWSTR* pExports;
} D3D12_DXIL_SUBOBJECT_TO_EXPORTS_ASSOCIATION;

typedef enum D3D12_HIT_GROUP_TYPE
{
    D3D12_HIT_GROUP_TYPE_TRIANGLES = 0x0,
    D3D12_HIT_GROUP_TYPE_PROCEDURAL_PRIMITIVE = 0x1,
} D3D12_HIT_GROUP_TYPE;

typedef struct D3D12_HIT_GROUP_DESC
{
    LPCWSTR HitGroupExport;
    D3D12_HIT_GROUP_TYPE Type;
    [annotation("_In_opt_")] LPCWSTR AnyHitShaderImport;
    [annotation("_In_opt_")] LPCWSTR ClosestHitShaderImport;
    [annotation("_In_opt_")] LPCWSTR IntersectionShaderImport;
} D3D12_HIT_GROUP_DESC;

typedef struct D3D12_RAYTRACING_SHADER_CONFIG
{
    UINT    MaxPayloadSizeInBytes;
    UINT    MaxAttributeSizeInBytes;
} D3D12_RAYTRACING_SHADER_CONFIG;

typedef struct D3D12_RAYTRACING_PIPELINE_CONFIG
{
    UINT    MaxTraceRecursionDepth;
} D3D12_RAYTRACING_PIPELINE_CONFIG;

typedef enum D3D12_RAYTRACING_PIPELINE_FLAGS
{
    D3D12_RAYTRACING_PIPELINE_FLAG_NONE                         = 0x0,
    D3D12_RAYTRACING_PIPELINE_FLAG_SKIP_TRIANGLES               = 0x100,
    D3D12_RAYTRACING_PIPELINE_FLAG_SKIP_PROCEDURAL_PRIMITIVES   = 0x200,
} D3D12_RAYTRACING_PIPELINE_FLAGS; 
cpp_quote("DEFINE_ENUM_FLAG_OPERATORS( D3D12_RAYTRACING_PIPELINE_FLAGS )")

typedef struct D3D12_RAYTRACING_PIPELINE_CONFIG1
{
    UINT                            MaxTraceRecursionDepth;
    D3D12_RAYTRACING_PIPELINE_FLAGS Flags;
} D3D12_RAYTRACING_PIPELINE_CONFIG1;

typedef struct D3D12_NODE_OUTPUT_OVERRIDES
{
    UINT                     OutputIndex;
    [annotation("_In_opt_")] const D3D12_NODE_ID* pNewName;
    [annotation("_In_opt_")] const BOOL* pAllowSparseNodes;
    [annotation("_In_opt_")] const UINT* pMaxRecords;
    [annotation("_In_opt_")] const UINT* pMaxRecordsSharedWithOutputIndex;
} D3D12_NODE_OUTPUT_OVERRIDES;

typedef struct D3D12_BROADCASTING_LAUNCH_OVERRIDES
{
    [annotation("_In_opt_")] const UINT* pLocalRootArgumentsTableIndex;
    [annotation("_In_opt_")] const BOOL* pProgramEntry;
    [annotation("_In_opt_")] const D3D12_NODE_ID* pNewName;
    [annotation("_In_opt_ ")] const D3D12_NODE_ID* pShareInputOf;
    [annotation("_In_reads_opt_(3)")] const UINT* pDispatchGrid;
    [annotation("_In_reads_opt_(3)")] const UINT* pMaxDispatchGrid;
    UINT          NumOutputOverrides;
    [annotation("_In_reads_opt_(NumOutputOverrides)")] const D3D12_NODE_OUTPUT_OVERRIDES* pOutputOverrides;
} D3D12_BROADCASTING_LAUNCH_OVERRIDES;

typedef struct D3D12_COALESCING_LAUNCH_OVERRIDES
{
    [annotation("_In_opt_")] const UINT* pLocalRootArgumentsTableIndex; 
    [annotation("_In_opt_")] const BOOL* pProgramEntry;
    [annotation("_In_opt_")] const D3D12_NODE_ID* pNewName;
    [annotation("_In_opt_")] const D3D12_NODE_ID* pShareInputOf;
    UINT          NumOutputOverrides; 
    [annotation("_In_reads_opt_(NumOutputOverrides)")] const D3D12_NODE_OUTPUT_OVERRIDES* pOutputOverrides;
} D3D12_COALESCING_LAUNCH_OVERRIDES;

typedef struct D3D12_THREAD_LAUNCH_OVERRIDES
{
    [annotation("_In_opt_")] const UINT* pLocalRootArgumentsTableIndex; 
    [annotation("_In_opt_")] const BOOL* pProgramEntry;
    [annotation("_In_opt_")] const D3D12_NODE_ID* pNewName;
    [annotation("_In_opt_")] const D3D12_NODE_ID* pShareInputOf;
    UINT          NumOutputOverrides; 
    [annotation("_In_reads_opt_(NumOutputOverrides)")] const D3D12_NODE_OUTPUT_OVERRIDES* pOutputOverrides;
} D3D12_THREAD_LAUNCH_OVERRIDES;

typedef struct D3D12_COMMON_COMPUTE_NODE_OVERRIDES
{
    [annotation("_In_opt_")] const UINT* pLocalRootArgumentsTableIndex;
    [annotation("_In_opt_")] const BOOL* pProgramEntry;
    [annotation("_In_opt_")] const D3D12_NODE_ID* pNewName;
    [annotation("_In_opt_")] const D3D12_NODE_ID* pShareInputOf;
    UINT          NumOutputOverrides;
    [annotation("_In_reads_opt_(NumOutputOverrides)")] const D3D12_NODE_OUTPUT_OVERRIDES* pOutputOverrides;
} D3D12_COMMON_COMPUTE_NODE_OVERRIDES;

typedef enum D3D12_NODE_OVERRIDES_TYPE
{
    D3D12_NODE_OVERRIDES_TYPE_NONE = 0,
    D3D12_NODE_OVERRIDES_TYPE_BROADCASTING_LAUNCH = 1,
    D3D12_NODE_OVERRIDES_TYPE_COALESCING_LAUNCH = 2,
    D3D12_NODE_OVERRIDES_TYPE_THREAD_LAUNCH = 3,
    D3D12_NODE_OVERRIDES_TYPE_COMMON_COMPUTE = 4,
} D3D12_NODE_OVERRIDES_TYPE;

typedef struct D3D12_SHADER_NODE
{
    LPCWSTR                   Shader; 
    D3D12_NODE_OVERRIDES_TYPE OverridesType;
    union
    {
        const D3D12_BROADCASTING_LAUNCH_OVERRIDES* pBroadcastingLaunchOverrides;
        const D3D12_COALESCING_LAUNCH_OVERRIDES* pCoalescingLaunchOverrides;
        const D3D12_THREAD_LAUNCH_OVERRIDES* pThreadLaunchOverrides;
        const D3D12_COMMON_COMPUTE_NODE_OVERRIDES* pCommonComputeNodeOverrides;
    };
} D3D12_SHADER_NODE;

typedef enum D3D12_NODE_TYPE
{
    D3D12_NODE_TYPE_SHADER = 0x0
} D3D12_NODE_TYPE;

typedef struct D3D12_NODE
{
    D3D12_NODE_TYPE NodeType;
    union
    {
        D3D12_SHADER_NODE Shader; // D3D12_NODE_TYPE_SHADER
    };
} D3D12_NODE;

typedef enum D3D12_WORK_GRAPH_FLAGS
{
    D3D12_WORK_GRAPH_FLAG_NONE = 0x0,
    D3D12_WORK_GRAPH_FLAG_INCLUDE_ALL_AVAILABLE_NODES = 0x1,
} D3D12_WORK_GRAPH_FLAGS;
cpp_quote("DEFINE_ENUM_FLAG_OPERATORS( D3D12_WORK_GRAPH_FLAGS )")

typedef struct D3D12_WORK_GRAPH_DESC
{
    LPCWSTR                ProgramName;
    D3D12_WORK_GRAPH_FLAGS Flags;
    UINT                   NumEntrypoints;
    [annotation("_In_reads_opt_(NumEntrypoints)")] const D3D12_NODE_ID* pEntrypoints;
    UINT    NumExplicitlyDefinedNodes;
    [annotation("_In_reads_opt_(NumExplicitlyDefinedNodes)")] const D3D12_NODE* pExplicitlyDefinedNodes; 
} D3D12_WORK_GRAPH_DESC;

typedef struct D3D12_GENERIC_PROGRAM_DESC
{
    LPCWSTR ProgramName;
    UINT NumExports;
    [annotation("_In_reads_(NumExports)")] LPCWSTR* pExports;
    UINT    NumSubobjects;
    [annotation("_In_reads_opt_(NumSubobjects)")] const D3D12_STATE_SUBOBJECT* const* ppSubobjects;
} D3D12_GENERIC_PROGRAM_DESC;

typedef enum D3D12_STATE_OBJECT_TYPE
{
    D3D12_STATE_OBJECT_TYPE_COLLECTION = 0,
    D3D12_STATE_OBJECT_TYPE_RAYTRACING_PIPELINE = 3,
    D3D12_STATE_OBJECT_TYPE_EXECUTABLE = 4
} D3D12_STATE_OBJECT_TYPE;

typedef struct D3D12_STATE_OBJECT_DESC
{
    D3D12_STATE_OBJECT_TYPE Type;
    UINT NumSubobjects;
    [annotation("_In_reads_(NumSubobjects)")] const D3D12_STATE_SUBOBJECT* pSubobjects; 
} D3D12_STATE_OBJECT_DESC;

typedef enum D3D12_RAYTRACING_GEOMETRY_FLAGS
{
    D3D12_RAYTRACING_GEOMETRY_FLAG_NONE = 0x0,
    D3D12_RAYTRACING_GEOMETRY_FLAG_OPAQUE = 0x1,
    D3D12_RAYTRACING_GEOMETRY_FLAG_NO_DUPLICATE_ANYHIT_INVOCATION = 0x2,
} D3D12_RAYTRACING_GEOMETRY_FLAGS;
cpp_quote("DEFINE_ENUM_FLAG_OPERATORS( D3D12_RAYTRACING_GEOMETRY_FLAGS )")

typedef enum D3D12_RAYTRACING_GEOMETRY_TYPE
{
    D3D12_RAYTRACING_GEOMETRY_TYPE_TRIANGLES,
    D3D12_RAYTRACING_GEOMETRY_TYPE_PROCEDURAL_PRIMITIVE_AABBS,
} D3D12_RAYTRACING_GEOMETRY_TYPE;

typedef enum D3D12_RAYTRACING_INSTANCE_FLAGS
{
    D3D12_RAYTRACING_INSTANCE_FLAG_NONE = 0x0,
    D3D12_RAYTRACING_INSTANCE_FLAG_TRIANGLE_CULL_DISABLE = 0x1,
    D3D12_RAYTRACING_INSTANCE_FLAG_TRIANGLE_FRONT_COUNTERCLOCKWISE = 0x2,
    D3D12_RAYTRACING_INSTANCE_FLAG_FORCE_OPAQUE = 0x4,
    D3D12_RAYTRACING_INSTANCE_FLAG_FORCE_NON_OPAQUE = 0x8
} D3D12_RAYTRACING_INSTANCE_FLAGS;
cpp_quote("DEFINE_ENUM_FLAG_OPERATORS( D3D12_RAYTRACING_INSTANCE_FLAGS )")

typedef struct D3D12_GPU_VIRTUAL_ADDRESS_AND_STRIDE
{
    D3D12_GPU_VIRTUAL_ADDRESS   StartAddress;
    UINT64                      StrideInBytes;
} D3D12_GPU_VIRTUAL_ADDRESS_AND_STRIDE;

typedef struct D3D12_GPU_VIRTUAL_ADDRESS_RANGE
{
    D3D12_GPU_VIRTUAL_ADDRESS   StartAddress;
    UINT64                      SizeInBytes;
} D3D12_GPU_VIRTUAL_ADDRESS_RANGE;

typedef struct D3D12_GPU_VIRTUAL_ADDRESS_RANGE_AND_STRIDE
{
    D3D12_GPU_VIRTUAL_ADDRESS   StartAddress;
    UINT64                      SizeInBytes;
    UINT64                      StrideInBytes;
} D3D12_GPU_VIRTUAL_ADDRESS_RANGE_AND_STRIDE;

typedef struct D3D12_RAYTRACING_GEOMETRY_TRIANGLES_DESC
{
    D3D12_GPU_VIRTUAL_ADDRESS               Transform3x4;
    DXGI_FORMAT                             IndexFormat;
    DXGI_FORMAT                             VertexFormat;
    UINT                                    IndexCount;
    UINT                                    VertexCount;
    D3D12_GPU_VIRTUAL_ADDRESS               IndexBuffer;
    D3D12_GPU_VIRTUAL_ADDRESS_AND_STRIDE    VertexBuffer;
} D3D12_RAYTRACING_GEOMETRY_TRIANGLES_DESC;

typedef struct D3D12_RAYTRACING_AABB
{
    FLOAT   MinX;
    FLOAT   MinY;
    FLOAT   MinZ;
    FLOAT   MaxX;
    FLOAT   MaxY;
    FLOAT   MaxZ;
} D3D12_RAYTRACING_AABB;

typedef struct D3D12_RAYTRACING_GEOMETRY_AABBS_DESC
{
    UINT64                                  AABBCount;
    D3D12_GPU_VIRTUAL_ADDRESS_AND_STRIDE    AABBs;
} D3D12_RAYTRACING_GEOMETRY_AABBS_DESC;

typedef enum D3D12_RAYTRACING_ACCELERATION_STRUCTURE_BUILD_FLAGS
{
    D3D12_RAYTRACING_ACCELERATION_STRUCTURE_BUILD_FLAG_NONE = 0x00,
    D3D12_RAYTRACING_ACCELERATION_STRUCTURE_BUILD_FLAG_ALLOW_UPDATE = 0x01,
    D3D12_RAYTRACING_ACCELERATION_STRUCTURE_BUILD_FLAG_ALLOW_COMPACTION = 0x02,
    D3D12_RAYTRACING_ACCELERATION_STRUCTURE_BUILD_FLAG_PREFER_FAST_TRACE = 0x04,
    D3D12_RAYTRACING_ACCELERATION_STRUCTURE_BUILD_FLAG_PREFER_FAST_BUILD = 0x08,
    D3D12_RAYTRACING_ACCELERATION_STRUCTURE_BUILD_FLAG_MINIMIZE_MEMORY = 0x10,
    D3D12_RAYTRACING_ACCELERATION_STRUCTURE_BUILD_FLAG_PERFORM_UPDATE = 0x20,
} D3D12_RAYTRACING_ACCELERATION_STRUCTURE_BUILD_FLAGS;
cpp_quote("DEFINE_ENUM_FLAG_OPERATORS( D3D12_RAYTRACING_ACCELERATION_STRUCTURE_BUILD_FLAGS )")

typedef enum D3D12_RAYTRACING_ACCELERATION_STRUCTURE_COPY_MODE
{
    D3D12_RAYTRACING_ACCELERATION_STRUCTURE_COPY_MODE_CLONE = 0x0,
    D3D12_RAYTRACING_ACCELERATION_STRUCTURE_COPY_MODE_COMPACT = 0x1,
    D3D12_RAYTRACING_ACCELERATION_STRUCTURE_COPY_MODE_VISUALIZATION_DECODE_FOR_TOOLS = 0x2,
    D3D12_RAYTRACING_ACCELERATION_STRUCTURE_COPY_MODE_SERIALIZE = 0x3,
    D3D12_RAYTRACING_ACCELERATION_STRUCTURE_COPY_MODE_DESERIALIZE = 0x4,
} D3D12_RAYTRACING_ACCELERATION_STRUCTURE_COPY_MODE;

typedef enum D3D12_RAYTRACING_ACCELERATION_STRUCTURE_TYPE
{
    D3D12_RAYTRACING_ACCELERATION_STRUCTURE_TYPE_TOP_LEVEL = 0x0,
    D3D12_RAYTRACING_ACCELERATION_STRUCTURE_TYPE_BOTTOM_LEVEL = 0x1
} D3D12_RAYTRACING_ACCELERATION_STRUCTURE_TYPE;

typedef enum D3D12_ELEMENTS_LAYOUT
{
    D3D12_ELEMENTS_LAYOUT_ARRAY = 0x0,
    D3D12_ELEMENTS_LAYOUT_ARRAY_OF_POINTERS = 0x1
} D3D12_ELEMENTS_LAYOUT;

typedef enum D3D12_RAYTRACING_ACCELERATION_STRUCTURE_POSTBUILD_INFO_TYPE
{
    D3D12_RAYTRACING_ACCELERATION_STRUCTURE_POSTBUILD_INFO_COMPACTED_SIZE = 0x0,
    D3D12_RAYTRACING_ACCELERATION_STRUCTURE_POSTBUILD_INFO_TOOLS_VISUALIZATION  = 0x1,
    D3D12_RAYTRACING_ACCELERATION_STRUCTURE_POSTBUILD_INFO_SERIALIZATION  = 0x2,
    D3D12_RAYTRACING_ACCELERATION_STRUCTURE_POSTBUILD_INFO_CURRENT_SIZE  = 0x3,
} D3D12_RAYTRACING_ACCELERATION_STRUCTURE_POSTBUILD_INFO_TYPE;

typedef struct D3D12_RAYTRACING_ACCELERATION_STRUCTURE_POSTBUILD_INFO_DESC
{
    D3D12_GPU_VIRTUAL_ADDRESS DestBuffer;
    D3D12_RAYTRACING_ACCELERATION_STRUCTURE_POSTBUILD_INFO_TYPE InfoType;    
} D3D12_RAYTRACING_ACCELERATION_STRUCTURE_POSTBUILD_INFO_DESC;

typedef struct D3D12_RAYTRACING_ACCELERATION_STRUCTURE_POSTBUILD_INFO_COMPACTED_SIZE_DESC
{
    UINT64 CompactedSizeInBytes;
} D3D12_RAYTRACING_ACCELERATION_STRUCTURE_POSTBUILD_INFO_COMPACTED_SIZE_DESC;

typedef struct D3D12_RAYTRACING_ACCELERATION_STRUCTURE_POSTBUILD_INFO_TOOLS_VISUALIZATION_DESC
{
    UINT64 DecodedSizeInBytes;
} D3D12_RAYTRACING_ACCELERATION_STRUCTURE_POSTBUILD_INFO_TOOLS_VISUALIZATION_DESC;

typedef struct D3D12_BUILD_RAYTRACING_ACCELERATION_STRUCTURE_TOOLS_VISUALIZATION_HEADER
{
    D3D12_RAYTRACING_ACCELERATION_STRUCTURE_TYPE Type;
    UINT NumDescs;
} D3D12_BUILD_RAYTRACING_ACCELERATION_STRUCTURE_TOOLS_VISUALIZATION_HEADER;
cpp_quote("// Regarding D3D12_BUILD_RAY_TRACING_ACCELERATION_STRUCTURE_TOOLS_VISUALIZATION_HEADER above,")
cpp_quote("// depending on Type field, NumDescs above is followed by either:")
cpp_quote("//       D3D12_RAY_TRACING_INSTANCE_DESC InstanceDescs[NumDescs]")
cpp_quote("//    or D3D12_RAY_TRACING_GEOMETRY_DESC GeometryDescs[NumDescs].")
cpp_quote("// There is 4 bytes of padding between GeometryDesc structs in the array so alignment is natural when viewed by CPU.")
cpp_quote("")

typedef struct D3D12_RAYTRACING_ACCELERATION_STRUCTURE_POSTBUILD_INFO_SERIALIZATION_DESC
{
    UINT64 SerializedSizeInBytes;
    UINT64 NumBottomLevelAccelerationStructurePointers;
} D3D12_RAYTRACING_ACCELERATION_STRUCTURE_POSTBUILD_INFO_SERIALIZATION_DESC;

typedef struct D3D12_SERIALIZED_DATA_DRIVER_MATCHING_IDENTIFIER
{
    GUID DriverOpaqueGUID;
    BYTE DriverOpaqueVersioningData[16];
} D3D12_SERIALIZED_DATA_DRIVER_MATCHING_IDENTIFIER;

typedef enum D3D12_SERIALIZED_DATA_TYPE
{
    D3D12_SERIALIZED_DATA_RAYTRACING_ACCELERATION_STRUCTURE = 0x0,
} D3D12_SERIALIZED_DATA_TYPE;

typedef enum D3D12_DRIVER_MATCHING_IDENTIFIER_STATUS
{
    D3D12_DRIVER_MATCHING_IDENTIFIER_COMPATIBLE_WITH_DEVICE = 0x0,
    D3D12_DRIVER_MATCHING_IDENTIFIER_UNSUPPORTED_TYPE = 0x1,
    D3D12_DRIVER_MATCHING_IDENTIFIER_UNRECOGNIZED = 0x2,
    D3D12_DRIVER_MATCHING_IDENTIFIER_INCOMPATIBLE_VERSION = 0x3,
    D3D12_DRIVER_MATCHING_IDENTIFIER_INCOMPATIBLE_TYPE = 0x4,
} D3D12_DRIVER_MATCHING_IDENTIFIER_STATUS;

typedef struct D3D12_SERIALIZED_RAYTRACING_ACCELERATION_STRUCTURE_HEADER
{
    D3D12_SERIALIZED_DATA_DRIVER_MATCHING_IDENTIFIER DriverMatchingIdentifier;
    UINT64 SerializedSizeInBytesIncludingHeader;
    UINT64 DeserializedSizeInBytes;
    UINT64 NumBottomLevelAccelerationStructurePointersAfterHeader;
} D3D12_SERIALIZED_RAYTRACING_ACCELERATION_STRUCTURE_HEADER;

typedef struct D3D12_RAYTRACING_ACCELERATION_STRUCTURE_POSTBUILD_INFO_CURRENT_SIZE_DESC
{
    UINT64 CurrentSizeInBytes;
} D3D12_RAYTRACING_ACCELERATION_STRUCTURE_POSTBUILD_INFO_CURRENT_SIZE_DESC;

typedef struct D3D12_RAYTRACING_INSTANCE_DESC
{
    FLOAT                       Transform[3][4];
    UINT                        InstanceID : 24;
    UINT                        InstanceMask : 8;
    UINT                        InstanceContributionToHitGroupIndex : 24;
    UINT                        Flags : 8;
    D3D12_GPU_VIRTUAL_ADDRESS   AccelerationStructure;
} D3D12_RAYTRACING_INSTANCE_DESC;

typedef struct D3D12_RAYTRACING_GEOMETRY_DESC
{
    D3D12_RAYTRACING_GEOMETRY_TYPE    Type;
    D3D12_RAYTRACING_GEOMETRY_FLAGS   Flags;
    union
    {
        D3D12_RAYTRACING_GEOMETRY_TRIANGLES_DESC  Triangles;
        D3D12_RAYTRACING_GEOMETRY_AABBS_DESC      AABBs;
    };
} D3D12_RAYTRACING_GEOMETRY_DESC;

typedef struct D3D12_BUILD_RAYTRACING_ACCELERATION_STRUCTURE_INPUTS
{
    D3D12_RAYTRACING_ACCELERATION_STRUCTURE_TYPE Type;
    D3D12_RAYTRACING_ACCELERATION_STRUCTURE_BUILD_FLAGS Flags;
    UINT NumDescs;
    D3D12_ELEMENTS_LAYOUT DescsLayout;
    union
    {
        D3D12_GPU_VIRTUAL_ADDRESS InstanceDescs;
        const D3D12_RAYTRACING_GEOMETRY_DESC* pGeometryDescs;
        const D3D12_RAYTRACING_GEOMETRY_DESC*const* ppGeometryDescs;
    };
} D3D12_BUILD_RAYTRACING_ACCELERATION_STRUCTURE_INPUTS;

typedef struct D3D12_BUILD_RAYTRACING_ACCELERATION_STRUCTURE_DESC
{
    D3D12_GPU_VIRTUAL_ADDRESS DestAccelerationStructureData;
    D3D12_BUILD_RAYTRACING_ACCELERATION_STRUCTURE_INPUTS Inputs;
    [annotation("_In_opt_")] D3D12_GPU_VIRTUAL_ADDRESS SourceAccelerationStructureData;
    D3D12_GPU_VIRTUAL_ADDRESS ScratchAccelerationStructureData;
} D3D12_BUILD_RAYTRACING_ACCELERATION_STRUCTURE_DESC;

typedef struct D3D12_RAYTRACING_ACCELERATION_STRUCTURE_PREBUILD_INFO
{
    UINT64 ResultDataMaxSizeInBytes;
    UINT64 ScratchDataSizeInBytes;
    UINT64 UpdateScratchDataSizeInBytes;
} D3D12_RAYTRACING_ACCELERATION_STRUCTURE_PREBUILD_INFO;

typedef enum D3D12_RAY_FLAGS
{
    D3D12_RAY_FLAG_NONE                            = 0x00,
    D3D12_RAY_FLAG_FORCE_OPAQUE                    = 0x01,
    D3D12_RAY_FLAG_FORCE_NON_OPAQUE                = 0x02,
    D3D12_RAY_FLAG_ACCEPT_FIRST_HIT_AND_END_SEARCH = 0x04,
    D3D12_RAY_FLAG_SKIP_CLOSEST_HIT_SHADER         = 0x08,
    D3D12_RAY_FLAG_CULL_BACK_FACING_TRIANGLES      = 0x10,
    D3D12_RAY_FLAG_CULL_FRONT_FACING_TRIANGLES     = 0x20,
    D3D12_RAY_FLAG_CULL_OPAQUE                     = 0x40,
    D3D12_RAY_FLAG_CULL_NON_OPAQUE                 = 0x80,
    D3D12_RAY_FLAG_SKIP_TRIANGLES                  = 0x100,
    D3D12_RAY_FLAG_SKIP_PROCEDURAL_PRIMITIVES      = 0x200,
} D3D12_RAY_FLAGS; 
cpp_quote( "DEFINE_ENUM_FLAG_OPERATORS( D3D12_RAY_FLAGS )" )

typedef enum D3D12_HIT_KIND
{
    D3D12_HIT_KIND_TRIANGLE_FRONT_FACE             = 0xfe,
    D3D12_HIT_KIND_TRIANGLE_BACK_FACE              = 0xff,
} D3D12_HIT_KIND; 

[uuid(8b4f173b-2fea-4b80-8f58-4307191ab95d), object, local, pointer_default(unique)]
interface ID3D12Device5
    : ID3D12Device4
{
    HRESULT CreateLifetimeTracker(
        [annotation("_In_")] ID3D12LifetimeOwner* pOwner,
        [in] REFIID riid, // Expected: ID3D12LifetimeTracker*
        [out, iid_is(riid), annotation("_COM_Outptr_")] void** ppvTracker
    );

    void RemoveDevice();

    HRESULT EnumerateMetaCommands(
        [annotation("_Inout_")] UINT* pNumMetaCommands,
        [annotation("_Out_writes_opt_(*pNumMetaCommands)")] D3D12_META_COMMAND_DESC* pDescs
    );

    HRESULT EnumerateMetaCommandParameters(
        [annotation("_In_")] REFGUID CommandId,
        [annotation("_In_")] D3D12_META_COMMAND_PARAMETER_STAGE Stage,
        [annotation("_Out_opt_")] UINT* pTotalStructureSizeInBytes,
        [annotation("_Inout_")] UINT* pParameterCount,
        [annotation("_Out_writes_opt_(*pParameterCount)")] D3D12_META_COMMAND_PARAMETER_DESC* pParameterDescs
    );

    HRESULT CreateMetaCommand(
        [annotation("_In_")] REFGUID CommandId,
        [annotation("_In_")] UINT NodeMask,
        [annotation("_In_reads_bytes_opt_(CreationParametersDataSizeInBytes)")] const void * pCreationParametersData,
        [annotation("_In_")] SIZE_T CreationParametersDataSizeInBytes,
        [in] REFIID riid,
        [out, iid_is(riid), annotation("_COM_Outptr_")] void** ppMetaCommand
    );

    HRESULT CreateStateObject(
        [in] const D3D12_STATE_OBJECT_DESC* pDesc,
        [in] REFIID riid, // ID3D12StateObject
        [out, iid_is(riid), annotation("_COM_Outptr_")] void** ppStateObject
    );

    void GetRaytracingAccelerationStructurePrebuildInfo(
        [annotation("_In_")] const D3D12_BUILD_RAYTRACING_ACCELERATION_STRUCTURE_INPUTS* pDesc,
        [annotation("_Out_")] D3D12_RAYTRACING_ACCELERATION_STRUCTURE_PREBUILD_INFO *pInfo
    );

    D3D12_DRIVER_MATCHING_IDENTIFIER_STATUS CheckDriverMatchingIdentifier(
        [annotation("_In_")] D3D12_SERIALIZED_DATA_TYPE SerializedDataType,
        [annotation("_In_")] const D3D12_SERIALIZED_DATA_DRIVER_MATCHING_IDENTIFIER* pIdentifierToCheck);
}


typedef enum D3D12_AUTO_BREADCRUMB_OP
{
    D3D12_AUTO_BREADCRUMB_OP_SETMARKER = 0,
    D3D12_AUTO_BREADCRUMB_OP_BEGINEVENT = 1,
    D3D12_AUTO_BREADCRUMB_OP_ENDEVENT = 2,
    D3D12_AUTO_BREADCRUMB_OP_DRAWINSTANCED = 3,
    D3D12_AUTO_BREADCRUMB_OP_DRAWINDEXEDINSTANCED = 4,
    D3D12_AUTO_BREADCRUMB_OP_EXECUTEINDIRECT = 5,
    D3D12_AUTO_BREADCRUMB_OP_DISPATCH = 6,
    D3D12_AUTO_BREADCRUMB_OP_COPYBUFFERREGION = 7,
    D3D12_AUTO_BREADCRUMB_OP_COPYTEXTUREREGION = 8,
    D3D12_AUTO_BREADCRUMB_OP_COPYRESOURCE = 9,
    D3D12_AUTO_BREADCRUMB_OP_COPYTILES = 10,
    D3D12_AUTO_BREADCRUMB_OP_RESOLVESUBRESOURCE = 11,
    D3D12_AUTO_BREADCRUMB_OP_CLEARRENDERTARGETVIEW = 12,
    D3D12_AUTO_BREADCRUMB_OP_CLEARUNORDEREDACCESSVIEW = 13,
    D3D12_AUTO_BREADCRUMB_OP_CLEARDEPTHSTENCILVIEW = 14,
    D3D12_AUTO_BREADCRUMB_OP_RESOURCEBARRIER = 15,
    D3D12_AUTO_BREADCRUMB_OP_EXECUTEBUNDLE = 16,
    D3D12_AUTO_BREADCRUMB_OP_PRESENT = 17,
    D3D12_AUTO_BREADCRUMB_OP_RESOLVEQUERYDATA = 18,
    D3D12_AUTO_BREADCRUMB_OP_BEGINSUBMISSION = 19,
    D3D12_AUTO_BREADCRUMB_OP_ENDSUBMISSION = 20,
    D3D12_AUTO_BREADCRUMB_OP_DECODEFRAME = 21,
    D3D12_AUTO_BREADCRUMB_OP_PROCESSFRAMES = 22,
    D3D12_AUTO_BREADCRUMB_OP_ATOMICCOPYBUFFERUINT = 23,
    D3D12_AUTO_BREADCRUMB_OP_ATOMICCOPYBUFFERUINT64 = 24,
    D3D12_AUTO_BREADCRUMB_OP_RESOLVESUBRESOURCEREGION = 25,
    D3D12_AUTO_BREADCRUMB_OP_WRITEBUFFERIMMEDIATE = 26,
    D3D12_AUTO_BREADCRUMB_OP_DECODEFRAME1 = 27,
    D3D12_AUTO_BREADCRUMB_OP_SETPROTECTEDRESOURCESESSION = 28,
    D3D12_AUTO_BREADCRUMB_OP_DECODEFRAME2 = 29,
    D3D12_AUTO_BREADCRUMB_OP_PROCESSFRAMES1 = 30,
    D3D12_AUTO_BREADCRUMB_OP_BUILDRAYTRACINGACCELERATIONSTRUCTURE = 31,
    D3D12_AUTO_BREADCRUMB_OP_EMITRAYTRACINGACCELERATIONSTRUCTUREPOSTBUILDINFO = 32,
    D3D12_AUTO_BREADCRUMB_OP_COPYRAYTRACINGACCELERATIONSTRUCTURE = 33,
    D3D12_AUTO_BREADCRUMB_OP_DISPATCHRAYS = 34,
    D3D12_AUTO_BREADCRUMB_OP_INITIALIZEMETACOMMAND = 35,
    D3D12_AUTO_BREADCRUMB_OP_EXECUTEMETACOMMAND = 36,
    D3D12_AUTO_BREADCRUMB_OP_ESTIMATEMOTION = 37,
    D3D12_AUTO_BREADCRUMB_OP_RESOLVEMOTIONVECTORHEAP = 38,
    D3D12_AUTO_BREADCRUMB_OP_SETPIPELINESTATE1 = 39,
    D3D12_AUTO_BREADCRUMB_OP_INITIALIZEEXTENSIONCOMMAND = 40,
    D3D12_AUTO_BREADCRUMB_OP_EXECUTEEXTENSIONCOMMAND = 41,
    D3D12_AUTO_BREADCRUMB_OP_DISPATCHMESH = 42,
    D3D12_AUTO_BREADCRUMB_OP_ENCODEFRAME = 43,
    D3D12_AUTO_BREADCRUMB_OP_RESOLVEENCODEROUTPUTMETADATA = 44,
    D3D12_AUTO_BREADCRUMB_OP_BARRIER = 45,
    D3D12_AUTO_BREADCRUMB_OP_BEGIN_COMMAND_LIST = 46,
    D3D12_AUTO_BREADCRUMB_OP_DISPATCHGRAPH = 47,
    D3D12_AUTO_BREADCRUMB_OP_SETPROGRAM = 48,
} D3D12_AUTO_BREADCRUMB_OP;

typedef struct D3D12_AUTO_BREADCRUMB_NODE
{
    const char *pCommandListDebugNameA;
    const wchar_t *pCommandListDebugNameW;
    const char *pCommandQueueDebugNameA;
    const wchar_t *pCommandQueueDebugNameW;
    ID3D12GraphicsCommandList *pCommandList;
    ID3D12CommandQueue *pCommandQueue;
    UINT32 BreadcrumbCount;
    const UINT32 *pLastBreadcrumbValue;
    const D3D12_AUTO_BREADCRUMB_OP *pCommandHistory;
    const struct D3D12_AUTO_BREADCRUMB_NODE *pNext; // pointer to next node
} D3D12_AUTO_BREADCRUMB_NODE;

typedef struct D3D12_DRED_BREADCRUMB_CONTEXT
{
    UINT BreadcrumbIndex;
    const wchar_t *pContextString;
} D3D12_DRED_BREADCRUMB_CONTEXT;

// !!! Must be castable to D3D12_AUTO_BREADCRUMB_NODE
typedef struct D3D12_AUTO_BREADCRUMB_NODE1
{
    const char *pCommandListDebugNameA;
    const wchar_t *pCommandListDebugNameW;
    const char *pCommandQueueDebugNameA;
    const wchar_t *pCommandQueueDebugNameW;
    ID3D12GraphicsCommandList *pCommandList;
    ID3D12CommandQueue *pCommandQueue;
    UINT BreadcrumbCount;
    const UINT *pLastBreadcrumbValue;
    const D3D12_AUTO_BREADCRUMB_OP *pCommandHistory;
    const struct D3D12_AUTO_BREADCRUMB_NODE1 *pNext; // pointer to next node
    UINT BreadcrumbContextsCount;
    D3D12_DRED_BREADCRUMB_CONTEXT *pBreadcrumbContexts;
} D3D12_AUTO_BREADCRUMB_NODE1;

// D3D12_DRED_VERSION
typedef enum D3D12_DRED_VERSION
{
    D3D12_DRED_VERSION_1_0 = 0x1,
    D3D12_DRED_VERSION_1_1 = 0x2,
    D3D12_DRED_VERSION_1_2 = 0x3,
    D3D12_DRED_VERSION_1_3 = 0x4,
} D3D12_DRED_VERSION;

typedef enum D3D12_DRED_FLAGS
{
    D3D12_DRED_FLAG_NONE                    = 0,
    D3D12_DRED_FLAG_FORCE_ENABLE            = 1,
    D3D12_DRED_FLAG_DISABLE_AUTOBREADCRUMBS = 2,
} D3D12_DRED_FLAGS;
cpp_quote("DEFINE_ENUM_FLAG_OPERATORS( D3D12_DRED_FLAGS )")

typedef enum D3D12_DRED_ENABLEMENT
{
    D3D12_DRED_ENABLEMENT_SYSTEM_CONTROLLED = 0,
    D3D12_DRED_ENABLEMENT_FORCED_OFF = 1,
    D3D12_DRED_ENABLEMENT_FORCED_ON = 2,
} D3D12_DRED_ENABLEMENT;

typedef struct D3D12_DEVICE_REMOVED_EXTENDED_DATA
{
    [annotation("_In_")] D3D12_DRED_FLAGS Flags; // Dred flags
    [annotation("_Out_")] D3D12_AUTO_BREADCRUMB_NODE *pHeadAutoBreadcrumbNode; // Pointer to head of a linked list of auto-breadcrumb data
} D3D12_DEVICE_REMOVED_EXTENDED_DATA;

typedef enum D3D12_DRED_ALLOCATION_TYPE
{
    // Enum starts at 19 and skips 26 to maintain compatibility with older D3D12 drivers
    D3D12_DRED_ALLOCATION_TYPE_COMMAND_QUEUE                    = 19,
    D3D12_DRED_ALLOCATION_TYPE_COMMAND_ALLOCATOR                = 20,
    D3D12_DRED_ALLOCATION_TYPE_PIPELINE_STATE                   = 21,
    D3D12_DRED_ALLOCATION_TYPE_COMMAND_LIST                     = 22,
    D3D12_DRED_ALLOCATION_TYPE_FENCE                            = 23,
    D3D12_DRED_ALLOCATION_TYPE_DESCRIPTOR_HEAP                  = 24,
    D3D12_DRED_ALLOCATION_TYPE_HEAP                             = 25,
    D3D12_DRED_ALLOCATION_TYPE_QUERY_HEAP                       = 27,
    D3D12_DRED_ALLOCATION_TYPE_COMMAND_SIGNATURE                = 28,
    D3D12_DRED_ALLOCATION_TYPE_PIPELINE_LIBRARY                 = 29,
    D3D12_DRED_ALLOCATION_TYPE_VIDEO_DECODER                    = 30,
    D3D12_DRED_ALLOCATION_TYPE_VIDEO_PROCESSOR                  = 32,
    D3D12_DRED_ALLOCATION_TYPE_RESOURCE                         = 34,
    D3D12_DRED_ALLOCATION_TYPE_PASS                             = 35,
    D3D12_DRED_ALLOCATION_TYPE_CRYPTOSESSION                    = 36,
    D3D12_DRED_ALLOCATION_TYPE_CRYPTOSESSIONPOLICY              = 37,
    D3D12_DRED_ALLOCATION_TYPE_PROTECTEDRESOURCESESSION         = 38,
    D3D12_DRED_ALLOCATION_TYPE_VIDEO_DECODER_HEAP               = 39,
    D3D12_DRED_ALLOCATION_TYPE_COMMAND_POOL                     = 40,
    D3D12_DRED_ALLOCATION_TYPE_COMMAND_RECORDER                 = 41,
    D3D12_DRED_ALLOCATION_TYPE_STATE_OBJECT                     = 42,
    D3D12_DRED_ALLOCATION_TYPE_METACOMMAND                      = 43,
    D3D12_DRED_ALLOCATION_TYPE_SCHEDULINGGROUP                  = 44,
    D3D12_DRED_ALLOCATION_TYPE_VIDEO_MOTION_ESTIMATOR           = 45,
    D3D12_DRED_ALLOCATION_TYPE_VIDEO_MOTION_VECTOR_HEAP         = 46,
    D3D12_DRED_ALLOCATION_TYPE_VIDEO_EXTENSION_COMMAND          = 47,
    D3D12_DRED_ALLOCATION_TYPE_VIDEO_ENCODER                    = 48,
    D3D12_DRED_ALLOCATION_TYPE_VIDEO_ENCODER_HEAP               = 49,


    D3D12_DRED_ALLOCATION_TYPE_INVALID = 0xFFFFFFFF
} D3D12_DRED_ALLOCATION_TYPE;

typedef struct D3D12_DRED_ALLOCATION_NODE
{
    const char *ObjectNameA;
    const wchar_t *ObjectNameW;
    D3D12_DRED_ALLOCATION_TYPE AllocationType;
    const struct D3D12_DRED_ALLOCATION_NODE *pNext;
} D3D12_DRED_ALLOCATION_NODE;

typedef struct D3D12_DRED_ALLOCATION_NODE1
{
    const char *ObjectNameA;
    const wchar_t *ObjectNameW;
    D3D12_DRED_ALLOCATION_TYPE AllocationType;
    const struct D3D12_DRED_ALLOCATION_NODE1 *pNext;
    const IUnknown *pObject;
} D3D12_DRED_ALLOCATION_NODE1;

typedef struct D3D12_DRED_AUTO_BREADCRUMBS_OUTPUT
{
    [annotation("_Out_")] const D3D12_AUTO_BREADCRUMB_NODE *pHeadAutoBreadcrumbNode; // Pointer to head of a null-terminated linked list of auto-breadcrumb data
} D3D12_DRED_AUTO_BREADCRUMBS_OUTPUT;

typedef struct D3D12_DRED_AUTO_BREADCRUMBS_OUTPUT1
{
    [annotation("_Out_")] const D3D12_AUTO_BREADCRUMB_NODE1 *pHeadAutoBreadcrumbNode; // Pointer to head of a null-terminated linked list of auto-breadcrumb data
} D3D12_DRED_AUTO_BREADCRUMBS_OUTPUT1;

typedef struct D3D12_DRED_PAGE_FAULT_OUTPUT
{
    D3D12_GPU_VIRTUAL_ADDRESS PageFaultVA; //If the device removal was due to GPU Fault, this contains the VA of the faulting op
    [annotation("_Out_")] const D3D12_DRED_ALLOCATION_NODE *pHeadExistingAllocationNode;
    [annotation("_Out_")] const D3D12_DRED_ALLOCATION_NODE *pHeadRecentFreedAllocationNode;
} D3D12_DRED_PAGE_FAULT_OUTPUT;

typedef struct D3D12_DRED_PAGE_FAULT_OUTPUT1
{
    D3D12_GPU_VIRTUAL_ADDRESS PageFaultVA; //If the device removal was due to GPU Fault, this contains the VA of the faulting op
    [annotation("_Out_")] const D3D12_DRED_ALLOCATION_NODE1 *pHeadExistingAllocationNode;
    [annotation("_Out_")] const D3D12_DRED_ALLOCATION_NODE1 *pHeadRecentFreedAllocationNode;
} D3D12_DRED_PAGE_FAULT_OUTPUT1;

typedef enum D3D12_DRED_PAGE_FAULT_FLAGS
{
    D3D12_DRED_PAGE_FAULT_FLAGS_NONE = 0,
} D3D12_DRED_PAGE_FAULT_FLAGS;
cpp_quote("DEFINE_ENUM_FLAG_OPERATORS( D3D12_DRED_PAGE_FAULT_FLAGS )")

typedef enum D3D12_DRED_DEVICE_STATE
{
    D3D12_DRED_DEVICE_STATE_UNKNOWN             = 0, // Only known if DRED event occurred
    D3D12_DRED_DEVICE_STATE_HUNG                = 3,
    D3D12_DRED_DEVICE_STATE_FAULT               = 6,
    D3D12_DRED_DEVICE_STATE_PAGEFAULT           = 7,
} D3D12_DRED_DEVICE_STATE;

typedef struct D3D12_DRED_PAGE_FAULT_OUTPUT2
{
    D3D12_GPU_VIRTUAL_ADDRESS PageFaultVA; //If the device removal was due to GPU Fault, this contains the VA of the faulting op
    [annotation("_Out_")] const D3D12_DRED_ALLOCATION_NODE1 *pHeadExistingAllocationNode;
    [annotation("_Out_")] const D3D12_DRED_ALLOCATION_NODE1 *pHeadRecentFreedAllocationNode;
    D3D12_DRED_PAGE_FAULT_FLAGS PageFaultFlags;
    
} D3D12_DRED_PAGE_FAULT_OUTPUT2;

typedef struct D3D12_DEVICE_REMOVED_EXTENDED_DATA1
{
    HRESULT DeviceRemovedReason;
    D3D12_DRED_AUTO_BREADCRUMBS_OUTPUT AutoBreadcrumbsOutput;
    D3D12_DRED_PAGE_FAULT_OUTPUT PageFaultOutput;
} D3D12_DEVICE_REMOVED_EXTENDED_DATA1;

typedef struct D3D12_DEVICE_REMOVED_EXTENDED_DATA2
{
    HRESULT DeviceRemovedReason;
    D3D12_DRED_AUTO_BREADCRUMBS_OUTPUT1 AutoBreadcrumbsOutput;
    D3D12_DRED_PAGE_FAULT_OUTPUT1 PageFaultOutput;
} D3D12_DEVICE_REMOVED_EXTENDED_DATA2;

typedef struct D3D12_DEVICE_REMOVED_EXTENDED_DATA3
{
    HRESULT DeviceRemovedReason;
    D3D12_DRED_AUTO_BREADCRUMBS_OUTPUT1 AutoBreadcrumbsOutput;
    D3D12_DRED_PAGE_FAULT_OUTPUT2 PageFaultOutput;
    D3D12_DRED_DEVICE_STATE DeviceState;
} D3D12_DEVICE_REMOVED_EXTENDED_DATA3;

typedef struct D3D12_VERSIONED_DEVICE_REMOVED_EXTENDED_DATA
{
    D3D12_DRED_VERSION Version; // Version of the DRED data
    union
    {
        D3D12_DEVICE_REMOVED_EXTENDED_DATA Dred_1_0;
        D3D12_DEVICE_REMOVED_EXTENDED_DATA1 Dred_1_1;
        D3D12_DEVICE_REMOVED_EXTENDED_DATA2 Dred_1_2;
        D3D12_DEVICE_REMOVED_EXTENDED_DATA3 Dred_1_3;
    };
} D3D12_VERSIONED_DEVICE_REMOVED_EXTENDED_DATA;

[uuid(82BC481C-6B9B-4030-AEDB-7EE3D1DF1E63), object, local, pointer_default(unique)]
interface ID3D12DeviceRemovedExtendedDataSettings
    : IUnknown
{
    void SetAutoBreadcrumbsEnablement(D3D12_DRED_ENABLEMENT Enablement);
    void SetPageFaultEnablement(D3D12_DRED_ENABLEMENT Enablement);
    void SetWatsonDumpEnablement(D3D12_DRED_ENABLEMENT Enablement);
};

[uuid(DBD5AE51-3317-4F0A-ADF9-1D7CEDCAAE0B), object, local, pointer_default(unique)]
interface ID3D12DeviceRemovedExtendedDataSettings1
    : ID3D12DeviceRemovedExtendedDataSettings
{
    void SetBreadcrumbContextEnablement(D3D12_DRED_ENABLEMENT Enablement);
};

[uuid(61552388-01ab-4008-a436-83db189566ea), object, local, pointer_default(unique)]
interface ID3D12DeviceRemovedExtendedDataSettings2
    : ID3D12DeviceRemovedExtendedDataSettings1
{
    void UseMarkersOnlyAutoBreadcrumbs(BOOL MarkersOnly);
};


[uuid(98931D33-5AE8-4791-AA3C-1A73A2934E71), object, local, pointer_default(unique)]
interface ID3D12DeviceRemovedExtendedData
    : IUnknown
{
    HRESULT GetAutoBreadcrumbsOutput([annotation("_Out_")] D3D12_DRED_AUTO_BREADCRUMBS_OUTPUT *pOutput);
    HRESULT GetPageFaultAllocationOutput([annotation("_Out_")] D3D12_DRED_PAGE_FAULT_OUTPUT *pOutput);
};

[uuid(9727A022-CF1D-4DDA-9EBA-EFFA653FC506), object, local, pointer_default(unique)]
interface ID3D12DeviceRemovedExtendedData1
    : ID3D12DeviceRemovedExtendedData
{
    HRESULT GetAutoBreadcrumbsOutput1([annotation("_Out_")] D3D12_DRED_AUTO_BREADCRUMBS_OUTPUT1 *pOutput);
    HRESULT GetPageFaultAllocationOutput1([annotation("_Out_")] D3D12_DRED_PAGE_FAULT_OUTPUT1 *pOutput);
};

[uuid(67FC5816-E4CA-4915-BF18-42541272DA54), object, local, pointer_default(unique)]
interface ID3D12DeviceRemovedExtendedData2
    : ID3D12DeviceRemovedExtendedData1
{
    HRESULT GetPageFaultAllocationOutput2([annotation("_Out_")] D3D12_DRED_PAGE_FAULT_OUTPUT2 *pOutput);
    D3D12_DRED_DEVICE_STATE GetDeviceState();
};

typedef enum D3D12_BACKGROUND_PROCESSING_MODE
{
    D3D12_BACKGROUND_PROCESSING_MODE_ALLOWED,
    D3D12_BACKGROUND_PROCESSING_MODE_ALLOW_INTRUSIVE_MEASUREMENTS,
    D3D12_BACKGROUND_PROCESSING_MODE_DISABLE_BACKGROUND_WORK,
    D3D12_BACKGROUND_PROCESSING_MODE_DISABLE_PROFILING_BY_SYSTEM,
} D3D12_BACKGROUND_PROCESSING_MODE;

typedef enum D3D12_MEASUREMENTS_ACTION
{
    D3D12_MEASUREMENTS_ACTION_KEEP_ALL,
    D3D12_MEASUREMENTS_ACTION_COMMIT_RESULTS,
    D3D12_MEASUREMENTS_ACTION_COMMIT_RESULTS_HIGH_PRIORITY,
    D3D12_MEASUREMENTS_ACTION_DISCARD_PREVIOUS,
} D3D12_MEASUREMENTS_ACTION;

[uuid(c70b221b-40e4-4a17-89af-025a0727a6dc), object, local, pointer_default(unique)]
interface ID3D12Device6 : ID3D12Device5
{
    HRESULT SetBackgroundProcessingMode(
        D3D12_BACKGROUND_PROCESSING_MODE Mode,
        D3D12_MEASUREMENTS_ACTION MeasurementsAction,
        [annotation("_In_opt_")] HANDLE hEventToSignalUponCompletion,
        [annotation("_Out_opt_")] BOOL* pbFurtherMeasurementsDesired);
};

cpp_quote("DEFINE_GUID(D3D12_PROTECTED_RESOURCES_SESSION_HARDWARE_PROTECTED,                           0x62B0084E, 0xC70E, 0x4DAA, 0xA1, 0x09, 0x30, 0xFF, 0x8D, 0x5A, 0x04, 0x82); ")

// D3D12_FEATURE_PROTECTED_RESOURCE_SESSION_TYPE_COUNT
typedef struct D3D12_FEATURE_DATA_PROTECTED_RESOURCE_SESSION_TYPE_COUNT
{
    UINT                                        NodeIndex;              // input
    UINT                                        Count;                  // output
} D3D12_FEATURE_DATA_PROTECTED_RESOURCE_SESSION_TYPE_COUNT;

// D3D12_FEATURE_PROTECTED_RESOURCE_SESSION_TYPES
typedef struct D3D12_FEATURE_DATA_PROTECTED_RESOURCE_SESSION_TYPES
{
    UINT                                        NodeIndex;              // input
    UINT                                        Count;                  // input
    GUID*                                       pTypes;                 // output
} D3D12_FEATURE_DATA_PROTECTED_RESOURCE_SESSION_TYPES;

typedef struct D3D12_PROTECTED_RESOURCE_SESSION_DESC1
{
    UINT NodeMask;
    D3D12_PROTECTED_RESOURCE_SESSION_FLAGS Flags;
    GUID ProtectionType;
} D3D12_PROTECTED_RESOURCE_SESSION_DESC1;

[uuid(D6F12DD6-76FB-406E-8961-4296EEFC0409), object, local, pointer_default(unique)]
interface ID3D12ProtectedResourceSession1
    : ID3D12ProtectedResourceSession
{
    D3D12_PROTECTED_RESOURCE_SESSION_DESC1 GetDesc1();
};

[uuid(5c014b53-68a1-4b9b-8bd1-dd6046b9358b), object, local, pointer_default(unique)]
interface ID3D12Device7 : ID3D12Device6
{
    HRESULT AddToStateObject(
        [in] const D3D12_STATE_OBJECT_DESC* pAddition,
        [in] ID3D12StateObject* pStateObjectToGrowFrom,
        [in] REFIID riid, // ID3D12StateObject
        [out, iid_is(riid), annotation("_COM_Outptr_")] void** ppNewStateObject
        );

    HRESULT CreateProtectedResourceSession1(
        [annotation("_In_")] const D3D12_PROTECTED_RESOURCE_SESSION_DESC1* pDesc,
        [annotation("_In_")] REFIID riid, // Expected: ID3D12ProtectedResourceSession1, 
        [out, iid_is(riid), annotation("_COM_Outptr_")] void** ppSession
        );
};

[uuid(9218E6BB-F944-4F7E-A75C-B1B2C7B701F3), object, local, pointer_default(unique)]
interface ID3D12Device8 : ID3D12Device7
{
    D3D12_RESOURCE_ALLOCATION_INFO GetResourceAllocationInfo2(
        UINT visibleMask,
        UINT numResourceDescs,
        [annotation("_In_reads_(numResourceDescs)")] const D3D12_RESOURCE_DESC1* pResourceDescs,
        [annotation("_Out_writes_opt_(numResourceDescs)")] D3D12_RESOURCE_ALLOCATION_INFO1* pResourceAllocationInfo1);

    HRESULT CreateCommittedResource2(
        [annotation("_In_")] const D3D12_HEAP_PROPERTIES* pHeapProperties,
        D3D12_HEAP_FLAGS HeapFlags,
        [annotation("_In_")] const D3D12_RESOURCE_DESC1* pDesc,
        D3D12_RESOURCE_STATES InitialResourceState,
        [annotation("_In_opt_")] const D3D12_CLEAR_VALUE* pOptimizedClearValue,
        [annotation("_In_opt_")] ID3D12ProtectedResourceSession* pProtectedSession,
        [in] REFIID riidResource, // Expected: ID3D12Resource1*
        [out, iid_is(riidResource), annotation("_COM_Outptr_opt_")] void** ppvResource);

    HRESULT CreatePlacedResource1(
        [annotation("_In_")] ID3D12Heap* pHeap,
        UINT64 HeapOffset,
        [annotation("_In_")] const D3D12_RESOURCE_DESC1* pDesc,
        D3D12_RESOURCE_STATES InitialState,
        [annotation("_In_opt_")] const D3D12_CLEAR_VALUE* pOptimizedClearValue,
        [in] REFIID riid, // Expected: ID3D12Resource*
        [out, iid_is(riid), annotation("_COM_Outptr_opt_")] void** ppvResource);

    void CreateSamplerFeedbackUnorderedAccessView(
        [annotation("_In_opt_")] ID3D12Resource* pTargetedResource,
        [annotation("_In_opt_")] ID3D12Resource* pFeedbackResource,
        [annotation("_In_")] D3D12_CPU_DESCRIPTOR_HANDLE DestDescriptor);

    void GetCopyableFootprints1(
        [annotation("_In_")] const D3D12_RESOURCE_DESC1* pResourceDesc,
        [annotation("_In_range_(0,D3D12_REQ_SUBRESOURCES)")] UINT FirstSubresource,
        [annotation("_In_range_(0,D3D12_REQ_SUBRESOURCES-FirstSubresource)")] UINT NumSubresources,
        UINT64 BaseOffset,
        [annotation("_Out_writes_opt_(NumSubresources)")] D3D12_PLACED_SUBRESOURCE_FOOTPRINT* pLayouts,
        [annotation("_Out_writes_opt_(NumSubresources)")] UINT* pNumRows,
        [annotation("_Out_writes_opt_(NumSubresources)")] UINT64* pRowSizeInBytes,
        [annotation("_Out_opt_")] UINT64* pTotalBytes);
};


[uuid(9D5E227A-4430-4161-88B3-3ECA6BB16E19), object, local, pointer_default(unique)]
interface ID3D12Resource1
    : ID3D12Resource
{
    HRESULT GetProtectedResourceSession(
        [in] REFIID riid, // Expected: ID3D12ProtectedResourceSession
        [out, iid_is(riid), annotation("_COM_Outptr_opt_")] void** ppProtectedSession);
}

[uuid(BE36EC3B-EA85-4AEB-A45A-E9D76404A495), object, local, pointer_default(unique)]
interface ID3D12Resource2
    : ID3D12Resource1
{
    D3D12_RESOURCE_DESC1 GetDesc1();
}



[uuid(572F7389-2168-49E3-9693-D6DF5871BF6D), object, local, pointer_default(unique)]
interface ID3D12Heap1
    : ID3D12Heap
{
    HRESULT GetProtectedResourceSession(
        [in] REFIID riid, // Expected: ID3D12ProtectedResourceSession
        [out, iid_is(riid), annotation("_COM_Outptr_opt_")] void** ppProtectedSession);
}


[uuid(6FDA83A7-B84C-4E38-9AC8-C7BD22016B3D), object, local, pointer_default(unique)]
interface ID3D12GraphicsCommandList3 : ID3D12GraphicsCommandList2
{
    void SetProtectedResourceSession(
        [annotation("_In_opt_")]ID3D12ProtectedResourceSession *pProtectedResourceSession
    );
}

// Beginning Access
typedef enum D3D12_RENDER_PASS_BEGINNING_ACCESS_TYPE
{
    D3D12_RENDER_PASS_BEGINNING_ACCESS_TYPE_DISCARD,
    D3D12_RENDER_PASS_BEGINNING_ACCESS_TYPE_PRESERVE,
    D3D12_RENDER_PASS_BEGINNING_ACCESS_TYPE_CLEAR,
    D3D12_RENDER_PASS_BEGINNING_ACCESS_TYPE_NO_ACCESS,
    D3D12_RENDER_PASS_BEGINNING_ACCESS_TYPE_PRESERVE_LOCAL_RENDER,
    D3D12_RENDER_PASS_BEGINNING_ACCESS_TYPE_PRESERVE_LOCAL_SRV,
    D3D12_RENDER_PASS_BEGINNING_ACCESS_TYPE_PRESERVE_LOCAL_UAV
} D3D12_RENDER_PASS_BEGINNING_ACCESS_TYPE;

typedef struct D3D12_RENDER_PASS_BEGINNING_ACCESS_CLEAR_PARAMETERS
{
    D3D12_CLEAR_VALUE ClearValue;
} D3D12_RENDER_PASS_BEGINNING_ACCESS_CLEAR_PARAMETERS;

typedef struct D3D12_RENDER_PASS_BEGINNING_ACCESS_PRESERVE_LOCAL_PARAMETERS
{
    UINT AdditionalWidth;
    UINT AdditionalHeight;
} D3D12_RENDER_PASS_BEGINNING_ACCESS_PRESERVE_LOCAL_PARAMETERS;

typedef struct D3D12_RENDER_PASS_BEGINNING_ACCESS
{
    D3D12_RENDER_PASS_BEGINNING_ACCESS_TYPE Type;

    union
    {
        D3D12_RENDER_PASS_BEGINNING_ACCESS_CLEAR_PARAMETERS Clear;
        D3D12_RENDER_PASS_BEGINNING_ACCESS_PRESERVE_LOCAL_PARAMETERS PreserveLocal;
    };
} D3D12_RENDER_PASS_BEGINNING_ACCESS;

// Ending Access
typedef enum D3D12_RENDER_PASS_ENDING_ACCESS_TYPE
{
    D3D12_RENDER_PASS_ENDING_ACCESS_TYPE_DISCARD,
    D3D12_RENDER_PASS_ENDING_ACCESS_TYPE_PRESERVE,
    D3D12_RENDER_PASS_ENDING_ACCESS_TYPE_RESOLVE,
    D3D12_RENDER_PASS_ENDING_ACCESS_TYPE_NO_ACCESS,
    D3D12_RENDER_PASS_ENDING_ACCESS_TYPE_PRESERVE_LOCAL_RENDER,
    D3D12_RENDER_PASS_ENDING_ACCESS_TYPE_PRESERVE_LOCAL_SRV,
    D3D12_RENDER_PASS_ENDING_ACCESS_TYPE_PRESERVE_LOCAL_UAV
} D3D12_RENDER_PASS_ENDING_ACCESS_TYPE;

typedef struct D3D12_RENDER_PASS_ENDING_ACCESS_RESOLVE_SUBRESOURCE_PARAMETERS
{
    UINT SrcSubresource;
    UINT DstSubresource;

    UINT DstX;
    UINT DstY;
    D3D12_RECT SrcRect; // (0, 0, 0, 0) == resolve entire resource.

} D3D12_RENDER_PASS_ENDING_ACCESS_RESOLVE_SUBRESOURCE_PARAMETERS;

typedef struct D3D12_RENDER_PASS_ENDING_ACCESS_RESOLVE_PARAMETERS
{
    ID3D12Resource* pSrcResource;
    ID3D12Resource* pDstResource;

    // Can be a subset of RT's array slices, but can't target subresources that were't part of RTV/DSV.
    UINT SubresourceCount;
    [annotation("_Field_size_full_(SubresourceCount)")] const D3D12_RENDER_PASS_ENDING_ACCESS_RESOLVE_SUBRESOURCE_PARAMETERS *pSubresourceParameters;

    DXGI_FORMAT Format;
    D3D12_RESOLVE_MODE ResolveMode;

    BOOL PreserveResolveSource;

} D3D12_RENDER_PASS_ENDING_ACCESS_RESOLVE_PARAMETERS;

typedef struct D3D12_RENDER_PASS_ENDING_ACCESS_PRESERVE_LOCAL_PARAMETERS
{
    UINT AdditionalWidth;
    UINT AdditionalHeight;
} D3D12_RENDER_PASS_ENDING_ACCESS_PRESERVE_LOCAL_PARAMETERS;

typedef struct D3D12_RENDER_PASS_ENDING_ACCESS
{
    D3D12_RENDER_PASS_ENDING_ACCESS_TYPE Type;

    union
    {
        D3D12_RENDER_PASS_ENDING_ACCESS_RESOLVE_PARAMETERS Resolve;
        D3D12_RENDER_PASS_ENDING_ACCESS_PRESERVE_LOCAL_PARAMETERS PreserveLocal;
    };
} D3D12_RENDER_PASS_ENDING_ACCESS;

// Render Target Desc
typedef struct D3D12_RENDER_PASS_RENDER_TARGET_DESC
{
    D3D12_CPU_DESCRIPTOR_HANDLE cpuDescriptor;

    D3D12_RENDER_PASS_BEGINNING_ACCESS BeginningAccess;
    D3D12_RENDER_PASS_ENDING_ACCESS EndingAccess;

} D3D12_RENDER_PASS_RENDER_TARGET_DESC;

// Depth-Stencil Desc
typedef struct D3D12_RENDER_PASS_DEPTH_STENCIL_DESC
{
    D3D12_CPU_DESCRIPTOR_HANDLE cpuDescriptor;

    D3D12_RENDER_PASS_BEGINNING_ACCESS DepthBeginningAccess;
    D3D12_RENDER_PASS_BEGINNING_ACCESS StencilBeginningAccess;

    D3D12_RENDER_PASS_ENDING_ACCESS DepthEndingAccess;
    D3D12_RENDER_PASS_ENDING_ACCESS StencilEndingAccess;

} D3D12_RENDER_PASS_DEPTH_STENCIL_DESC;

typedef enum D3D12_RENDER_PASS_FLAGS
{
    D3D12_RENDER_PASS_FLAG_NONE                   = 0x0,
    D3D12_RENDER_PASS_FLAG_ALLOW_UAV_WRITES       = 0x1,
    D3D12_RENDER_PASS_FLAG_SUSPENDING_PASS        = 0x2,
    D3D12_RENDER_PASS_FLAG_RESUMING_PASS          = 0x4,
    D3D12_RENDER_PASS_FLAG_BIND_READ_ONLY_DEPTH   = 0x8,
    D3D12_RENDER_PASS_FLAG_BIND_READ_ONLY_STENCIL = 0x10
} D3D12_RENDER_PASS_FLAGS;
cpp_quote("DEFINE_ENUM_FLAG_OPERATORS( D3D12_RENDER_PASS_FLAGS )")

[uuid(DBB84C27-36CE-4FC9-B801-F048C46AC570), object, local, pointer_default(unique)]
interface ID3D12MetaCommand : ID3D12Pageable
{
    UINT64 GetRequiredParameterResourceSize([annotation("_In_")] D3D12_META_COMMAND_PARAMETER_STAGE Stage, [annotation("_In_")]  UINT ParameterIndex);
}

typedef struct D3D12_DISPATCH_RAYS_DESC
{
    D3D12_GPU_VIRTUAL_ADDRESS_RANGE RayGenerationShaderRecord;
    D3D12_GPU_VIRTUAL_ADDRESS_RANGE_AND_STRIDE MissShaderTable;
    D3D12_GPU_VIRTUAL_ADDRESS_RANGE_AND_STRIDE HitGroupTable;
    D3D12_GPU_VIRTUAL_ADDRESS_RANGE_AND_STRIDE CallableShaderTable;
    UINT Width;
    UINT Height;
    UINT Depth;
} D3D12_DISPATCH_RAYS_DESC;

typedef enum D3D12_SET_WORK_GRAPH_FLAGS
{
    D3D12_SET_WORK_GRAPH_FLAG_NONE       = 0x0,
    D3D12_SET_WORK_GRAPH_FLAG_INITIALIZE = 0x1, 
} D3D12_SET_WORK_GRAPH_FLAGS;
cpp_quote( "DEFINE_ENUM_FLAG_OPERATORS( D3D12_SET_WORK_GRAPH_FLAGS )" )

typedef struct D3D12_SET_WORK_GRAPH_DESC
{  
    D3D12_PROGRAM_IDENTIFIER ProgramIdentifier;
    D3D12_SET_WORK_GRAPH_FLAGS Flags;
    D3D12_GPU_VIRTUAL_ADDRESS_RANGE BackingMemory;
    D3D12_GPU_VIRTUAL_ADDRESS_RANGE_AND_STRIDE NodeLocalRootArgumentsTable;
} D3D12_SET_WORK_GRAPH_DESC;

typedef struct D3D12_SET_RAYTRACING_PIPELINE_DESC
{  
    D3D12_PROGRAM_IDENTIFIER ProgramIdentifier;
} D3D12_SET_RAYTRACING_PIPELINE_DESC;

typedef struct D3D12_SET_GENERIC_PIPELINE_DESC
{  
    D3D12_PROGRAM_IDENTIFIER ProgramIdentifier;
} D3D12_SET_GENERIC_PIPELINE_DESC;

typedef enum D3D12_PROGRAM_TYPE
{
    D3D12_PROGRAM_TYPE_GENERIC_PIPELINE = 1,
    D3D12_PROGRAM_TYPE_RAYTRACING_PIPELINE = 4,
    D3D12_PROGRAM_TYPE_WORK_GRAPH = 5
} D3D12_PROGRAM_TYPE;

typedef struct D3D12_SET_PROGRAM_DESC
{
    D3D12_PROGRAM_TYPE Type;
    union
    {
        D3D12_SET_GENERIC_PIPELINE_DESC     GenericPipeline;
        D3D12_SET_RAYTRACING_PIPELINE_DESC  RaytracingPipeline;
        D3D12_SET_WORK_GRAPH_DESC           WorkGraph; 
    };
} D3D12_SET_PROGRAM_DESC;

typedef enum D3D12_DISPATCH_MODE
{
    D3D12_DISPATCH_MODE_NODE_CPU_INPUT = 0, // D3D12_NODE_CPU_INPUT
    D3D12_DISPATCH_MODE_NODE_GPU_INPUT = 1, // D3D12_NODE_GPU_INPUT in GPU memory
    D3D12_DISPATCH_MODE_MULTI_NODE_CPU_INPUT = 2, // D3D12_MULTI_NODE_CPU_INPUT
    D3D12_DISPATCH_MODE_MULTI_NODE_GPU_INPUT = 3 // D3D12_MULTI_NODE_GPU_INPUT in GPU memory
} D3D12_DISPATCH_MODE;

typedef struct D3D12_NODE_CPU_INPUT
{
    UINT EntrypointIndex;
    UINT NumRecords;
    const void* pRecords;
    UINT64 RecordStrideInBytes;
} D3D12_NODE_CPU_INPUT;

typedef struct D3D12_NODE_GPU_INPUT
{
    UINT EntrypointIndex;
    UINT NumRecords;
    D3D12_GPU_VIRTUAL_ADDRESS_AND_STRIDE Records;
} D3D12_NODE_GPU_INPUT;

typedef struct D3D12_MULTI_NODE_CPU_INPUT
{
    UINT NumNodeInputs;
    const D3D12_NODE_CPU_INPUT* pNodeInputs;
    UINT64 NodeInputStrideInBytes;
} D3D12_MULTI_NODE_CPU_INPUT;

typedef struct D3D12_MULTI_NODE_GPU_INPUT
{
    UINT NumNodeInputs;
    D3D12_GPU_VIRTUAL_ADDRESS_AND_STRIDE NodeInputs; // D3D12_GPU_NODE_INPUT array
} D3D12_MULTI_NODE_GPU_INPUT;

typedef struct D3D12_DISPATCH_GRAPH_DESC
{
    D3D12_DISPATCH_MODE Mode;
    union
    {
        D3D12_NODE_CPU_INPUT       NodeCPUInput;
        D3D12_GPU_VIRTUAL_ADDRESS  NodeGPUInput;
        D3D12_MULTI_NODE_CPU_INPUT MultiNodeCPUInput;
        D3D12_GPU_VIRTUAL_ADDRESS  MultiNodeGPUInput;
    };    
} D3D12_DISPATCH_GRAPH_DESC;

[uuid(8754318e-d3a9-4541-98cf-645b50dc4874), object, local, pointer_default(unique)]
interface ID3D12GraphicsCommandList4 : ID3D12GraphicsCommandList3
{
    void BeginRenderPass(
        [annotation("_In_")]                             UINT NumRenderTargets,
        [annotation("_In_reads_opt_(NumRenderTargets)")] const D3D12_RENDER_PASS_RENDER_TARGET_DESC* pRenderTargets,
        [annotation("_In_opt_")]                         const D3D12_RENDER_PASS_DEPTH_STENCIL_DESC* pDepthStencil,
        D3D12_RENDER_PASS_FLAGS                          Flags
        );

    void EndRenderPass();

    void InitializeMetaCommand(
        [annotation("_In_")] ID3D12MetaCommand * pMetaCommand,
        [annotation("_In_reads_bytes_opt_(InitializationParametersDataSizeInBytes)")] const void * pInitializationParametersData,
        [annotation("_In_")] SIZE_T InitializationParametersDataSizeInBytes
    );

    void ExecuteMetaCommand(
        [annotation("_In_")] ID3D12MetaCommand * pMetaCommand,
        [annotation("_In_reads_bytes_opt_(ExecutionParametersDataSizeInBytes)")] const void * pExecutionParametersData,
        [annotation("_In_")] SIZE_T ExecutionParametersDataSizeInBytes
    );

    void BuildRaytracingAccelerationStructure(
        [annotation("_In_")] const D3D12_BUILD_RAYTRACING_ACCELERATION_STRUCTURE_DESC* pDesc,
        [annotation("_In_")] UINT NumPostbuildInfoDescs,
        [annotation("_In_reads_opt_(NumPostbuildInfoDescs)")] const D3D12_RAYTRACING_ACCELERATION_STRUCTURE_POSTBUILD_INFO_DESC* pPostbuildInfoDescs
        );

    void EmitRaytracingAccelerationStructurePostbuildInfo(
        [annotation("_In_")] const D3D12_RAYTRACING_ACCELERATION_STRUCTURE_POSTBUILD_INFO_DESC* pDesc,
        [annotation("_In_")] UINT NumSourceAccelerationStructures,
        [annotation("_In_reads_( NumSourceAccelerationStructures )")] const D3D12_GPU_VIRTUAL_ADDRESS* pSourceAccelerationStructureData);

    void CopyRaytracingAccelerationStructure(
        [annotation("_In_")] D3D12_GPU_VIRTUAL_ADDRESS DestAccelerationStructureData,
        [annotation("_In_")] D3D12_GPU_VIRTUAL_ADDRESS SourceAccelerationStructureData,
        [annotation("_In_")] D3D12_RAYTRACING_ACCELERATION_STRUCTURE_COPY_MODE Mode);

    void SetPipelineState1(
        [annotation("_In_")] ID3D12StateObject* pStateObject);

    void DispatchRays(
        [annotation("_In_")] const D3D12_DISPATCH_RAYS_DESC* pDesc);
}

typedef enum D3D12_SHADER_CACHE_MODE
{
    D3D12_SHADER_CACHE_MODE_MEMORY,
    D3D12_SHADER_CACHE_MODE_DISK,
} D3D12_SHADER_CACHE_MODE;

typedef enum D3D12_SHADER_CACHE_FLAGS
{
    D3D12_SHADER_CACHE_FLAG_NONE = 0x0,
    D3D12_SHADER_CACHE_FLAG_DRIVER_VERSIONED = 0x1,
    D3D12_SHADER_CACHE_FLAG_USE_WORKING_DIR = 0x2,
} D3D12_SHADER_CACHE_FLAGS;
cpp_quote("DEFINE_ENUM_FLAG_OPERATORS( D3D12_SHADER_CACHE_FLAGS )")

typedef struct D3D12_SHADER_CACHE_SESSION_DESC
{
    GUID Identifier;
    D3D12_SHADER_CACHE_MODE Mode;
    D3D12_SHADER_CACHE_FLAGS Flags;

    UINT MaximumInMemoryCacheSizeBytes;
    UINT MaximumInMemoryCacheEntries;

    UINT MaximumValueFileSizeBytes;

    UINT64 Version;
} D3D12_SHADER_CACHE_SESSION_DESC;

typedef enum D3D12_BARRIER_LAYOUT
{
    D3D12_BARRIER_LAYOUT_UNDEFINED = 0xffffffff,
    D3D12_BARRIER_LAYOUT_COMMON = 0,
    D3D12_BARRIER_LAYOUT_PRESENT = 0,
    D3D12_BARRIER_LAYOUT_GENERIC_READ,
    D3D12_BARRIER_LAYOUT_RENDER_TARGET,
    D3D12_BARRIER_LAYOUT_UNORDERED_ACCESS,
    D3D12_BARRIER_LAYOUT_DEPTH_STENCIL_WRITE,
    D3D12_BARRIER_LAYOUT_DEPTH_STENCIL_READ,
    D3D12_BARRIER_LAYOUT_SHADER_RESOURCE,
    D3D12_BARRIER_LAYOUT_COPY_SOURCE,
    D3D12_BARRIER_LAYOUT_COPY_DEST,
    D3D12_BARRIER_LAYOUT_RESOLVE_SOURCE,
    D3D12_BARRIER_LAYOUT_RESOLVE_DEST,
    D3D12_BARRIER_LAYOUT_SHADING_RATE_SOURCE,
    D3D12_BARRIER_LAYOUT_VIDEO_DECODE_READ,
    D3D12_BARRIER_LAYOUT_VIDEO_DECODE_WRITE,
    D3D12_BARRIER_LAYOUT_VIDEO_PROCESS_READ,
    D3D12_BARRIER_LAYOUT_VIDEO_PROCESS_WRITE,
    D3D12_BARRIER_LAYOUT_VIDEO_ENCODE_READ,
    D3D12_BARRIER_LAYOUT_VIDEO_ENCODE_WRITE,
    D3D12_BARRIER_LAYOUT_DIRECT_QUEUE_COMMON,
    D3D12_BARRIER_LAYOUT_DIRECT_QUEUE_GENERIC_READ,
    D3D12_BARRIER_LAYOUT_DIRECT_QUEUE_UNORDERED_ACCESS,
    D3D12_BARRIER_LAYOUT_DIRECT_QUEUE_SHADER_RESOURCE,
    D3D12_BARRIER_LAYOUT_DIRECT_QUEUE_COPY_SOURCE,
    D3D12_BARRIER_LAYOUT_DIRECT_QUEUE_COPY_DEST,
    D3D12_BARRIER_LAYOUT_COMPUTE_QUEUE_COMMON,
    D3D12_BARRIER_LAYOUT_COMPUTE_QUEUE_GENERIC_READ,
    D3D12_BARRIER_LAYOUT_COMPUTE_QUEUE_UNORDERED_ACCESS,
    D3D12_BARRIER_LAYOUT_COMPUTE_QUEUE_SHADER_RESOURCE,
    D3D12_BARRIER_LAYOUT_COMPUTE_QUEUE_COPY_SOURCE,
    D3D12_BARRIER_LAYOUT_COMPUTE_QUEUE_COPY_DEST,
    D3D12_BARRIER_LAYOUT_VIDEO_QUEUE_COMMON,

} D3D12_BARRIER_LAYOUT;

typedef enum D3D12_BARRIER_SYNC
{
    D3D12_BARRIER_SYNC_NONE                                                     = 0x0,
    D3D12_BARRIER_SYNC_ALL                                                      = 0x1,
    D3D12_BARRIER_SYNC_DRAW                                                     = 0x2,
    D3D12_BARRIER_SYNC_INDEX_INPUT                                              = 0x4,
    D3D12_BARRIER_SYNC_VERTEX_SHADING                                           = 0x8,
    D3D12_BARRIER_SYNC_PIXEL_SHADING                                            = 0x10,
    D3D12_BARRIER_SYNC_DEPTH_STENCIL                                            = 0x20,
    D3D12_BARRIER_SYNC_RENDER_TARGET                                            = 0x40,
    D3D12_BARRIER_SYNC_COMPUTE_SHADING                                          = 0x80,
    D3D12_BARRIER_SYNC_RAYTRACING                                               = 0x100,
    D3D12_BARRIER_SYNC_COPY                                                     = 0x200,
    D3D12_BARRIER_SYNC_RESOLVE                                                  = 0x400,
    D3D12_BARRIER_SYNC_EXECUTE_INDIRECT                                         = 0x800,
    D3D12_BARRIER_SYNC_PREDICATION                                              = 0x800, // Aliased with SYNC_EXECUTE_INDIRECT
    D3D12_BARRIER_SYNC_ALL_SHADING                                              = 0x1000,
    D3D12_BARRIER_SYNC_NON_PIXEL_SHADING                                        = 0x2000,
    D3D12_BARRIER_SYNC_EMIT_RAYTRACING_ACCELERATION_STRUCTURE_POSTBUILD_INFO    = 0x4000,
    D3D12_BARRIER_SYNC_CLEAR_UNORDERED_ACCESS_VIEW                              = 0x8000,
    D3D12_BARRIER_SYNC_VIDEO_DECODE                                             = 0x100000,
    D3D12_BARRIER_SYNC_VIDEO_PROCESS                                            = 0x200000,
    D3D12_BARRIER_SYNC_VIDEO_ENCODE                                             = 0x400000,
    D3D12_BARRIER_SYNC_BUILD_RAYTRACING_ACCELERATION_STRUCTURE                  = 0x800000,
    D3D12_BARRIER_SYNC_COPY_RAYTRACING_ACCELERATION_STRUCTURE                   = 0x1000000,
    D3D12_BARRIER_SYNC_SPLIT                                                    = 0x80000000,
} D3D12_BARRIER_SYNC;
cpp_quote( "DEFINE_ENUM_FLAG_OPERATORS( D3D12_BARRIER_SYNC )" )

typedef enum D3D12_BARRIER_ACCESS
{
    D3D12_BARRIER_ACCESS_COMMON                                     = 0,
    D3D12_BARRIER_ACCESS_VERTEX_BUFFER                              = 0x1,
    D3D12_BARRIER_ACCESS_CONSTANT_BUFFER                            = 0x2,
    D3D12_BARRIER_ACCESS_INDEX_BUFFER                               = 0x4,
    D3D12_BARRIER_ACCESS_RENDER_TARGET                              = 0x8,
    D3D12_BARRIER_ACCESS_UNORDERED_ACCESS                           = 0x10,
    D3D12_BARRIER_ACCESS_DEPTH_STENCIL_WRITE                        = 0x20,
    D3D12_BARRIER_ACCESS_DEPTH_STENCIL_READ                         = 0x40,
    D3D12_BARRIER_ACCESS_SHADER_RESOURCE                            = 0x80,
    D3D12_BARRIER_ACCESS_STREAM_OUTPUT                              = 0x100,
    D3D12_BARRIER_ACCESS_INDIRECT_ARGUMENT                          = 0x200,
    D3D12_BARRIER_ACCESS_PREDICATION                                = 0x200, // Aliased with ACCESS_INDIRECT_ARGUMENT
    D3D12_BARRIER_ACCESS_COPY_DEST                                  = 0x400,
    D3D12_BARRIER_ACCESS_COPY_SOURCE                                = 0x800,
    D3D12_BARRIER_ACCESS_RESOLVE_DEST                               = 0x1000,
    D3D12_BARRIER_ACCESS_RESOLVE_SOURCE                             = 0x2000,
    D3D12_BARRIER_ACCESS_RAYTRACING_ACCELERATION_STRUCTURE_READ     = 0x4000,
    D3D12_BARRIER_ACCESS_RAYTRACING_ACCELERATION_STRUCTURE_WRITE    = 0x8000,
    D3D12_BARRIER_ACCESS_SHADING_RATE_SOURCE                        = 0x10000,
    D3D12_BARRIER_ACCESS_VIDEO_DECODE_READ                          = 0x20000,
    D3D12_BARRIER_ACCESS_VIDEO_DECODE_WRITE                         = 0x40000,
    D3D12_BARRIER_ACCESS_VIDEO_PROCESS_READ                         = 0x80000,
    D3D12_BARRIER_ACCESS_VIDEO_PROCESS_WRITE                        = 0x100000,
    D3D12_BARRIER_ACCESS_VIDEO_ENCODE_READ                          = 0x200000,
    D3D12_BARRIER_ACCESS_VIDEO_ENCODE_WRITE                         = 0x400000,
    D3D12_BARRIER_ACCESS_NO_ACCESS                                  = 0x80000000,
} D3D12_BARRIER_ACCESS;
cpp_quote( "DEFINE_ENUM_FLAG_OPERATORS( D3D12_BARRIER_ACCESS )" )

typedef enum D3D12_BARRIER_TYPE
{
    D3D12_BARRIER_TYPE_GLOBAL,
    D3D12_BARRIER_TYPE_TEXTURE,
    D3D12_BARRIER_TYPE_BUFFER,
} D3D12_BARRIER_TYPE;

typedef enum D3D12_TEXTURE_BARRIER_FLAGS
{
    D3D12_TEXTURE_BARRIER_FLAG_NONE = 0x0,
    D3D12_TEXTURE_BARRIER_FLAG_DISCARD = 0x1,
} D3D12_TEXTURE_BARRIER_FLAGS;
cpp_quote("DEFINE_ENUM_FLAG_OPERATORS( D3D12_TEXTURE_BARRIER_FLAGS )")

typedef struct D3D12_BARRIER_SUBRESOURCE_RANGE
{
    UINT IndexOrFirstMipLevel;
    UINT NumMipLevels;
    UINT FirstArraySlice;
    UINT NumArraySlices;
    UINT FirstPlane;
    UINT NumPlanes;
} D3D12_BARRIER_SUBRESOURCE_RANGE;

typedef struct D3D12_GLOBAL_BARRIER
{
    D3D12_BARRIER_SYNC SyncBefore;
    D3D12_BARRIER_SYNC SyncAfter;
    D3D12_BARRIER_ACCESS AccessBefore;
    D3D12_BARRIER_ACCESS AccessAfter;
} D3D12_GLOBAL_BARRIER;

typedef struct D3D12_TEXTURE_BARRIER
{
    D3D12_BARRIER_SYNC SyncBefore;
    D3D12_BARRIER_SYNC SyncAfter;
    D3D12_BARRIER_ACCESS AccessBefore;
    D3D12_BARRIER_ACCESS AccessAfter;
    D3D12_BARRIER_LAYOUT LayoutBefore;
    D3D12_BARRIER_LAYOUT LayoutAfter;
    [annotation("_In_")] ID3D12Resource *pResource;
    D3D12_BARRIER_SUBRESOURCE_RANGE Subresources;
    D3D12_TEXTURE_BARRIER_FLAGS Flags;
} D3D12_TEXTURE_BARRIER;

typedef struct D3D12_BUFFER_BARRIER
{
    D3D12_BARRIER_SYNC SyncBefore;
    D3D12_BARRIER_SYNC SyncAfter;
    D3D12_BARRIER_ACCESS AccessBefore;
    D3D12_BARRIER_ACCESS AccessAfter;
    [annotation("_In_")] ID3D12Resource *pResource;
    UINT64 Offset;
    UINT64 Size;
} D3D12_BUFFER_BARRIER;

typedef struct D3D12_BARRIER_GROUP
{
    D3D12_BARRIER_TYPE Type;
    UINT32 NumBarriers;
    union
    {
        [annotation("_In_reads_(NumBarriers)")] const D3D12_GLOBAL_BARRIER *pGlobalBarriers;
        [annotation("_In_reads_(NumBarriers)")] const D3D12_TEXTURE_BARRIER *pTextureBarriers;
        [annotation("_In_reads_(NumBarriers)")] const D3D12_BUFFER_BARRIER *pBufferBarriers;
    };
} D3D12_BARRIER_GROUP;

[uuid(28e2495d-0f64-4ae4-a6ec-129255dc49a8), object, local, pointer_default(unique)]
interface ID3D12ShaderCacheSession
    : ID3D12DeviceChild
{
    HRESULT FindValue(
        [in, annotation("_In_reads_bytes_(KeySize)")] const void* pKey,
        UINT KeySize,
        [out, annotation("_Out_writes_bytes_(*pValueSize)")] void* pValue,
        [annotation("_Inout_")] UINT* pValueSize);
    HRESULT StoreValue(
        [in, annotation("_In_reads_bytes_(KeySize)")] const void* pKey,
        UINT KeySize,
        [in, annotation("_In_reads_bytes_(ValueSize)")] const void* pValue,
        UINT ValueSize);

    void SetDeleteOnDestroy();
    D3D12_SHADER_CACHE_SESSION_DESC GetDesc();
};

typedef enum D3D12_SHADER_CACHE_KIND_FLAGS
{
    D3D12_SHADER_CACHE_KIND_FLAG_IMPLICIT_D3D_CACHE_FOR_DRIVER = 0x1,
    D3D12_SHADER_CACHE_KIND_FLAG_IMPLICIT_D3D_CONVERSIONS = 0x2,
    D3D12_SHADER_CACHE_KIND_FLAG_IMPLICIT_DRIVER_MANAGED = 0x4,
    D3D12_SHADER_CACHE_KIND_FLAG_APPLICATION_MANAGED = 0x8,
} D3D12_SHADER_CACHE_KIND_FLAGS;
cpp_quote("DEFINE_ENUM_FLAG_OPERATORS( D3D12_SHADER_CACHE_KIND_FLAGS )")

typedef enum D3D12_SHADER_CACHE_CONTROL_FLAGS
{
    D3D12_SHADER_CACHE_CONTROL_FLAG_DISABLE = 0x1,
    D3D12_SHADER_CACHE_CONTROL_FLAG_ENABLE = 0x2,
    D3D12_SHADER_CACHE_CONTROL_FLAG_CLEAR = 0x4,
} D3D12_SHADER_CACHE_CONTROL_FLAGS;
cpp_quote("DEFINE_ENUM_FLAG_OPERATORS( D3D12_SHADER_CACHE_CONTROL_FLAGS )")

[uuid(4c80e962-f032-4f60-bc9e-ebc2cfa1d83c), object, local, pointer_default(unique)]
interface ID3D12Device9
    : ID3D12Device8
{
    HRESULT CreateShaderCacheSession(
        [annotation("_In_")] const D3D12_SHADER_CACHE_SESSION_DESC* pDesc,
        REFIID riid,
        [annotation("_COM_Outptr_opt_")] void** ppvSession);

    HRESULT ShaderCacheControl(
        D3D12_SHADER_CACHE_KIND_FLAGS Kinds,
        D3D12_SHADER_CACHE_CONTROL_FLAGS Control);

    HRESULT CreateCommandQueue1(
        [annotation("_In_")] const D3D12_COMMAND_QUEUE_DESC* pDesc,
        REFIID CreatorID,
        [in] REFIID riid, // Expected: ID3D12CommandQueue
        [out, iid_is(riid), annotation("_COM_Outptr_")] void** ppCommandQueue
    );
};

[uuid(517f8718-aa66-49f9-b02b-a7ab89c06031), object, local, pointer_default(unique)]
interface ID3D12Device10
    : ID3D12Device9
{
    HRESULT CreateCommittedResource3(
        [annotation("_In_")] const D3D12_HEAP_PROPERTIES* pHeapProperties,
        D3D12_HEAP_FLAGS HeapFlags,
        [annotation("_In_")] const D3D12_RESOURCE_DESC1* pDesc,
        D3D12_BARRIER_LAYOUT InitialLayout,
        [annotation("_In_opt_")] const D3D12_CLEAR_VALUE* pOptimizedClearValue,
        [annotation("_In_opt_")] ID3D12ProtectedResourceSession* pProtectedSession,
        UINT32 NumCastableFormats,
        [annotation("_In_opt_count_(NumCastableFormats)")] const DXGI_FORMAT *pCastableFormats,
        [in] REFIID riidResource, // Expected: ID3D12Resource1*
        [out, iid_is(riidResource), annotation("_COM_Outptr_opt_")] void** ppvResource);

    HRESULT CreatePlacedResource2(
        [annotation("_In_")] ID3D12Heap* pHeap,
        UINT64 HeapOffset,
        [annotation("_In_")] const D3D12_RESOURCE_DESC1* pDesc,
        D3D12_BARRIER_LAYOUT InitialLayout,
        [annotation("_In_opt_")] const D3D12_CLEAR_VALUE* pOptimizedClearValue,
        UINT32 NumCastableFormats,
        [annotation("_In_opt_count_(NumCastableFormats)")] const DXGI_FORMAT *pCastableFormats,
        [in] REFIID riid, // Expected: ID3D12Resource*
        [out, iid_is(riid), annotation("_COM_Outptr_opt_")] void** ppvResource);

    HRESULT CreateReservedResource2(
        [annotation("_In_")] const D3D12_RESOURCE_DESC* pDesc,
        D3D12_BARRIER_LAYOUT InitialLayout,
        [annotation("_In_opt_")] const D3D12_CLEAR_VALUE* pOptimizedClearValue,
        [annotation("_In_opt_")] ID3D12ProtectedResourceSession *pProtectedSession,
        UINT32 NumCastableFormats,
        [annotation("_In_opt_count_(NumCastableFormats)")] const DXGI_FORMAT *pCastableFormats,
        [in] REFIID riid, // Expected: ID3D12Resource1*
        [out, iid_is(riid), annotation("_COM_Outptr_opt_")] void** ppvResource
    );
};

[uuid(5405c344-d457-444e-b4dd-2366e45aee39), object, local, pointer_default(unique)]
interface ID3D12Device11
    : ID3D12Device10
{
    void CreateSampler2(
        [annotation("_In_")] const D3D12_SAMPLER_DESC2* pDesc,
        [annotation("_In_")] D3D12_CPU_DESCRIPTOR_HANDLE DestDescriptor);
};

[uuid(5af5c532-4c91-4cd0-b541-15a405395fc5), object, local, pointer_default(unique)]
interface ID3D12Device12
    : ID3D12Device11
{
    D3D12_RESOURCE_ALLOCATION_INFO GetResourceAllocationInfo3(
        UINT visibleMask,
        UINT numResourceDescs,
        [annotation("_In_reads_(numResourceDescs)")] const D3D12_RESOURCE_DESC1* pResourceDescs,
        [annotation("_In_opt_count_(numResourceDescs)")] const UINT32* pNumCastableFormats,
        [annotation("_In_opt_count_(numResourceDescs)")] const DXGI_FORMAT *const *ppCastableFormats,
        [annotation("_Out_writes_opt_(numResourceDescs)")] D3D12_RESOURCE_ALLOCATION_INFO1* pResourceAllocationInfo1);
};

[uuid(14eecffc-4df8-40f7-a118-5c816f45695e), object, local, pointer_default(unique)]
interface ID3D12Device13
    : ID3D12Device12
{
    HRESULT OpenExistingHeapFromAddress1(
        [annotation("_In_")] const void* pAddress,
        SIZE_T size,
        [in] REFIID riid, // Expected: ID3D12Heap
        [out, iid_is(riid), annotation("_COM_Outptr_")] void** ppvHeap
    );
};

[uuid(5f6e592d-d895-44c2-8e4a-88ad4926d323), object, local, pointer_default(unique)]
interface ID3D12Device14 : ID3D12Device13
{
    HRESULT CreateRootSignatureFromSubobjectInLibrary(
        [annotation("_In_")] UINT nodeMask,
        [annotation("_In_reads_(blobLengthInBytes)")] const void* pLibraryBlob,
        [annotation("_In_")] SIZE_T blobLengthInBytes,
        [annotation("_In_opt_")] LPCWSTR subobjectName,
        [in] REFIID riid, // Expected ID3D12RootSignature
        [out, iid_is(riid), annotation("_COM_Outptr_")] void** ppvRootSignature);
};


[uuid(bc66d368-**************-fc87dc79e476), object, local, pointer_default(unique)]
interface ID3D12VirtualizationGuestDevice
    : IUnknown
{
    HRESULT ShareWithHost(
        [annotation("_In_")] ID3D12DeviceChild* pObject,
        [annotation("_Out_")] HANDLE* pHandle
    );

    HRESULT CreateFenceFd(
        [annotation("_In_")] ID3D12Fence *pFence,
        UINT64 FenceValue,
        [annotation("_Out_")] int *pFenceFd
    );
};


[uuid(7071e1f0-e84b-4b33-974f-12fa49de65c5), object, local, pointer_default(unique)]
interface ID3D12Tools
    : IUnknown
{
    void EnableShaderInstrumentation(BOOL bEnable);
    BOOL ShaderInstrumentationEnabled();
}


typedef struct D3D12_SUBRESOURCE_DATA
{
    const void* pData;
    LONG_PTR RowPitch;
    LONG_PTR SlicePitch;
} D3D12_SUBRESOURCE_DATA;

typedef struct D3D12_MEMCPY_DEST
{
    void* pData;
    SIZE_T RowPitch;
    SIZE_T SlicePitch;
} D3D12_MEMCPY_DEST;



cpp_quote( "#if !defined( D3D12_IGNORE_SDK_LAYERS ) ")
cpp_quote( "#include \"d3d12sdklayers.h\" ")
cpp_quote( "#endif ")


cpp_quote("")
cpp_quote("///////////////////////////////////////////////////////////////////////////")
cpp_quote("// D3D12CreateDevice")
cpp_quote("// ------------------")
cpp_quote("//")
cpp_quote("// pAdapter")
cpp_quote("//      If NULL, D3D12CreateDevice will choose the primary adapter.")
cpp_quote("//      If non-NULL, D3D12CreateDevice will use the provided adapter.")
cpp_quote("// MinimumFeatureLevel")
cpp_quote("//      The minimum feature level required for successful device creation.")
cpp_quote("// riid")
cpp_quote("//      The interface IID of the device to be returned. Expected: ID3D12Device.")
cpp_quote("// ppDevice")
cpp_quote("//      Pointer to returned interface. May be NULL.")
cpp_quote("//")
cpp_quote("// Return Values")
cpp_quote("//  Any of those documented for ")
cpp_quote("//          CreateDXGIFactory1")
cpp_quote("//          IDXGIFactory::EnumAdapters")
cpp_quote("//          D3D12CreateDevice")
cpp_quote("//")
cpp_quote("///////////////////////////////////////////////////////////////////////////")
cpp_quote("typedef HRESULT (WINAPI* PFN_D3D12_CREATE_DEVICE)( _In_opt_ IUnknown*, ")
cpp_quote("    D3D_FEATURE_LEVEL, ")
cpp_quote("    _In_ REFIID, _COM_Outptr_opt_ void** );")
cpp_quote("")
cpp_quote("HRESULT WINAPI D3D12CreateDevice(")
cpp_quote("    _In_opt_ IUnknown* pAdapter,")
cpp_quote("    D3D_FEATURE_LEVEL MinimumFeatureLevel,")
cpp_quote("    _In_ REFIID riid, // Expected: ID3D12Device")
cpp_quote("    _COM_Outptr_opt_ void** ppDevice );")
cpp_quote("")

cpp_quote("")
cpp_quote("typedef HRESULT (WINAPI* PFN_D3D12_GET_DEBUG_INTERFACE)( _In_ REFIID, _COM_Outptr_opt_ void** );")
cpp_quote("")
cpp_quote("HRESULT WINAPI D3D12GetDebugInterface( _In_ REFIID riid, _COM_Outptr_opt_ void** ppvDebug );")
cpp_quote("")

cpp_quote("// --------------------------------------------------------------------------------------------------------------------------------")
cpp_quote("// D3D12EnableExperimentalFeatures")
cpp_quote("//")
cpp_quote("// Pass in a list of feature GUIDs to be enabled together.")
cpp_quote("// ")
cpp_quote("// If a particular feature requires some configuration information on enablement, it will have")
cpp_quote("// a configuration struct that can be passed alongside the GUID.")
cpp_quote("// ")
cpp_quote("// Some features might use an interface IID as the GUID.  For these, once the feature is enabled via")
cpp_quote("// D3D12EnableExperimentalFeatures, D3D12GetDebugInterface can then be called with the IID to retrieve the interface")
cpp_quote("// for manipulating the feature.  This allows for control that might not cleanly be expressed by just ")
cpp_quote("// the configuration struct that D3D12EnableExperimentalFeatures provides.")
cpp_quote("//")
cpp_quote("// If this method is called and a change to existing feature enablement is made, ")
cpp_quote("// all current D3D12 devices are set to DEVICE_REMOVED state, since under the covers there is really only one")
cpp_quote("// singleton device for a process.  Removing the devices when configuration changes prevents")
cpp_quote("// mismatched expectations of how a device is supposed to work after it has been created from the app's point of view.")
cpp_quote("//")
cpp_quote("// The call returns E_NOINTERFACE if an unrecognized feature is passed in or Windows Developer mode is not on.")
cpp_quote("// The call returns E_INVALIDARG if the configuration of a feature is incorrect, the set of features passed")
cpp_quote("// in are known to be incompatible with each other, or other errors.")
cpp_quote("// Returns S_OK otherwise.")
cpp_quote("//")
cpp_quote("// --------------------------------------------------------------------------------------------------------------------------------")
cpp_quote("HRESULT WINAPI D3D12EnableExperimentalFeatures(")
cpp_quote("    UINT                                    NumFeatures,")
cpp_quote("    _In_count_(NumFeatures) const IID*     pIIDs,")
cpp_quote("    _In_opt_count_(NumFeatures) void*      pConfigurationStructs,")
cpp_quote("    _In_opt_count_(NumFeatures) UINT*      pConfigurationStructSizes);")
cpp_quote("")
cpp_quote("// --------------------------------------------------------------------------------------------------------------------------------")
cpp_quote("// Experimental Feature: D3D12ExperimentalShaderModels")
cpp_quote("//")
cpp_quote("// Use with D3D12EnableExperimentalFeatures to enable experimental shader model support,")
cpp_quote("// meaning shader models that haven't been finalized for use in retail.")
cpp_quote("//")
cpp_quote("// Enabling D3D12ExperimentalShaderModels needs no configuration struct, pass NULL in the pConfigurationStructs array.")
cpp_quote("//")
cpp_quote("// --------------------------------------------------------------------------------------------------------------------------------")
cpp_quote("static const UUID D3D12ExperimentalShaderModels = { /* 76f5573e-f13a-40f5-b297-81ce9e18933f */")
cpp_quote("    0x76f5573e,")
cpp_quote("    0xf13a,")
cpp_quote("    0x40f5,")
cpp_quote("    { 0xb2, 0x97, 0x81, 0xce, 0x9e, 0x18, 0x93, 0x3f }")
cpp_quote("};")
cpp_quote("// --------------------------------------------------------------------------------------------------------------------------------")
cpp_quote("// Experimental Feature: D3D12TiledResourceTier4")
cpp_quote("//")
cpp_quote("// Use with D3D12EnableExperimentalFeatures to enable tiled resource tier 4 support,")
cpp_quote("// meaning texture tile data-inheritance is allowed.")
cpp_quote("//")
cpp_quote("// Enabling D3D12TiledResourceTier4 needs no configuration struct, pass NULL in the pConfigurationStructs array.")
cpp_quote("//")
cpp_quote("// --------------------------------------------------------------------------------------------------------------------------------")
cpp_quote("static const UUID D3D12TiledResourceTier4 = { /* c9c4725f-a81a-4f56-8c5b-c51039d694fb */")
cpp_quote("    0xc9c4725f,")
cpp_quote("    0xa81a,")
cpp_quote("    0x4f56,")
cpp_quote("    { 0x8c, 0x5b, 0xc5, 0x10, 0x39, 0xd6, 0x94, 0xfb }")
cpp_quote("};")

cpp_quote("// --------------------------------------------------------------------------------------------------------------------------------")
cpp_quote("// D3D12GetInterface")
cpp_quote("//")
cpp_quote("// Retrieve Global D3D12 Interface.")
cpp_quote("//")
cpp_quote("")
cpp_quote("DEFINE_GUID(CLSID_D3D12Debug,                        0xf2352aeb, 0xdd84, 0x49fe, 0xb9, 0x7b, 0xa9, 0xdc, 0xfd, 0xcc, 0x1b, 0x4f);")
cpp_quote("DEFINE_GUID(CLSID_D3D12Tools,                        0xe38216b1, 0x3c8c, 0x4833, 0xaa, 0x09, 0x0a, 0x06, 0xb6, 0x5d, 0x96, 0xc8);")
cpp_quote("DEFINE_GUID(CLSID_D3D12DeviceRemovedExtendedData,    0x4a75bbc4, 0x9ff4, 0x4ad8, 0x9f, 0x18, 0xab, 0xae, 0x84, 0xdc, 0x5f, 0xf2);")
cpp_quote("DEFINE_GUID(CLSID_D3D12SDKConfiguration,             0x7cda6aca, 0xa03e, 0x49c8, 0x94, 0x58, 0x03, 0x34, 0xd2, 0x0e, 0x07, 0xce);")
cpp_quote("DEFINE_GUID(CLSID_D3D12DeviceFactory,                0x114863bf, 0xc386, 0x4aee, 0xb3, 0x9d, 0x8f, 0x0b, 0xbb, 0x06, 0x29, 0x55);")
cpp_quote("DEFINE_GUID(CLSID_D3D12DSRDeviceFactory,             0xbb6dd27e, 0x94a9, 0x41a6, 0x9f, 0x1b, 0x13, 0x37, 0x72, 0x17, 0x24, 0x28);")
cpp_quote("")
cpp_quote("typedef HRESULT (WINAPI* PFN_D3D12_GET_INTERFACE)( _In_ REFCLSID, _In_ REFIID, _COM_Outptr_opt_ void** );")
cpp_quote("")
cpp_quote("HRESULT WINAPI D3D12GetInterface( _In_ REFCLSID rclsid, _In_ REFIID riid, _COM_Outptr_opt_ void** ppvDebug );")
cpp_quote("")

//==================================================================================================================================
//
// SDK Configuration
//
//==================================================================================================================================

[uuid(e9eb5314-33aa-42b2-a718-d77f58b1f1c7), object, local, pointer_default(unique)]
interface ID3D12SDKConfiguration
    : IUnknown
{
    HRESULT SetSDKVersion(
        UINT SDKVersion,
        [annotation("_In_z_")] LPCSTR SDKPath
        );
}

[uuid(8aaf9303-ad25-48b9-9a57-d9c37e009d9f), object, local, pointer_default(unique)]
interface ID3D12SDKConfiguration1
    : ID3D12SDKConfiguration
{
    HRESULT CreateDeviceFactory(
        UINT SDKVersion,
        [annotation("_In_")] LPCSTR SDKPath,
        REFIID riid, // Expected: ID3D12DeviceFactory
        [annotation("_COM_Outptr_")] void **ppvFactory
    );
    void FreeUnusedSDKs();
}

typedef enum D3D12_DEVICE_FACTORY_FLAGS
{
    D3D12_DEVICE_FACTORY_FLAG_NONE = 0,
    D3D12_DEVICE_FACTORY_FLAG_ALLOW_RETURNING_EXISTING_DEVICE = 0x1,
    D3D12_DEVICE_FACTORY_FLAG_ALLOW_RETURNING_INCOMPATIBLE_EXISTING_DEVICE = 0x2,
    D3D12_DEVICE_FACTORY_FLAG_DISALLOW_STORING_NEW_DEVICE_AS_SINGLETON = 0x4,
} D3D12_DEVICE_FACTORY_FLAGS;
cpp_quote("DEFINE_ENUM_FLAG_OPERATORS( D3D12_DEVICE_FACTORY_FLAGS )")

[uuid(61f307d3-d34e-4e7c-8374-3ba4de23cccb), object, local, pointer_default(unique)]
interface ID3D12DeviceFactory
    : IUnknown
{
    HRESULT InitializeFromGlobalState();
    HRESULT ApplyToGlobalState();

    HRESULT SetFlags(D3D12_DEVICE_FACTORY_FLAGS flags);
    D3D12_DEVICE_FACTORY_FLAGS GetFlags();

    HRESULT GetConfigurationInterface(
        REFCLSID clsid,
        REFIID   iid,
        [annotation("_COM_Outptr_")] void **ppv);

    HRESULT EnableExperimentalFeatures(
       UINT      NumFeatures,
       [annotation("_In_reads_(NumFeatures)")]     const IID *pIIDs,
       [annotation("_In_reads_opt_(NumFeatures)")] void *pConfigurationStructs,
       [annotation("_In_reads_opt_(NumFeatures)")] UINT *pConfigurationStructSizes);

    HRESULT CreateDevice(
        [annotation("_In_opt_")] IUnknown *adapter,
        D3D_FEATURE_LEVEL FeatureLevel,
        REFIID riid,
        [annotation("_COM_Outptr_opt_")] void **ppvDevice);
};

typedef enum D3D12_DEVICE_FLAGS
{
    D3D12_DEVICE_FLAG_NONE = 0,
    D3D12_DEVICE_FLAG_DEBUG_LAYER_ENABLED = 0x1,
    D3D12_DEVICE_FLAG_GPU_BASED_VALIDATION_ENABLED = 0x2,
    D3D12_DEVICE_FLAG_SYNCHRONIZED_COMMAND_QUEUE_VALIDATION_DISABLED = 0x4,
    D3D12_DEVICE_FLAG_DRED_AUTO_BREADCRUMBS_ENABLED = 0x8,
    D3D12_DEVICE_FLAG_DRED_PAGE_FAULT_REPORTING_ENABLED = 0x10,
    D3D12_DEVICE_FLAG_DRED_WATSON_REPORTING_ENABLED = 0x20,
    D3D12_DEVICE_FLAG_DRED_BREADCRUMB_CONTEXT_ENABLED = 0x40,
    D3D12_DEVICE_FLAG_DRED_USE_MARKERS_ONLY_BREADCRUMBS = 0x80,
    D3D12_DEVICE_FLAG_SHADER_INSTRUMENTATION_ENABLED = 0x100,
    D3D12_DEVICE_FLAG_AUTO_DEBUG_NAME_ENABLED = 0x200,
    D3D12_DEVICE_FLAG_FORCE_LEGACY_STATE_VALIDATION = 0x400,
} D3D12_DEVICE_FLAGS;
cpp_quote("DEFINE_ENUM_FLAG_OPERATORS( D3D12_DEVICE_FLAGS )")

typedef struct D3D12_DEVICE_CONFIGURATION_DESC
{
    D3D12_DEVICE_FLAGS Flags;
    UINT GpuBasedValidationFlags; // D3D12_GPU_BASED_VALIDATION_FLAGS from d3d12sdklayers.h
    UINT SDKVersion;
    UINT NumEnabledExperimentalFeatures;
} D3D12_DEVICE_CONFIGURATION_DESC;

[uuid(78dbf87b-f766-422b-a61c-c8c446bdb9ad), object, local, pointer_default(unique)]
interface ID3D12DeviceConfiguration
    : IUnknown
{
    D3D12_DEVICE_CONFIGURATION_DESC GetDesc();
    HRESULT GetEnabledExperimentalFeatures([annotation("_Out_writes_(NumGuids)")] GUID *pGuids, UINT NumGuids);

    HRESULT SerializeVersionedRootSignature(
        [annotation("_In_")] const D3D12_VERSIONED_ROOT_SIGNATURE_DESC *pDesc,
        [annotation("_COM_Outptr_")] ID3DBlob **ppResult,
        [annotation("_Always_(_Outptr_opt_result_maybenull_)")] ID3DBlob **ppError);

    HRESULT CreateVersionedRootSignatureDeserializer(
        [annotation("_In_reads_bytes_(Size)")] const void *pBlob,
        SIZE_T Size,
        REFIID riid,
        [annotation("_COM_Outptr_")] void **ppvDeserializer);
};

[uuid(ed342442-6343-4e16-bb82-a3a577874e56), object, local, pointer_default(unique)]
interface ID3D12DeviceConfiguration1
    : ID3D12DeviceConfiguration
{
    HRESULT CreateVersionedRootSignatureDeserializerFromSubobjectInLibrary(
        [annotation("_In_reads_bytes_(Size)")] const void* pLibraryBlob,
        SIZE_T Size,
        LPCWSTR RootSignatureSubobjectName,
        REFIID riid,
        [annotation("_COM_Outptr_")] void** ppvDeserializer);
};


typedef enum D3D12_AXIS_SHADING_RATE
{
    D3D12_AXIS_SHADING_RATE_1X = 0x0,
    D3D12_AXIS_SHADING_RATE_2X = 0x1,
    D3D12_AXIS_SHADING_RATE_4X = 0x2
} D3D12_AXIS_SHADING_RATE;


cpp_quote("#define D3D12_SHADING_RATE_X_AXIS_SHIFT 2")
cpp_quote("#define D3D12_SHADING_RATE_VALID_MASK 3")

cpp_quote("#define D3D12_MAKE_COARSE_SHADING_RATE(x,y) ((x) << D3D12_SHADING_RATE_X_AXIS_SHIFT | (y))")

cpp_quote("#define D3D12_GET_COARSE_SHADING_RATE_X_AXIS(x) (((x) >> D3D12_SHADING_RATE_X_AXIS_SHIFT) & D3D12_SHADING_RATE_VALID_MASK)")
cpp_quote("#define D3D12_GET_COARSE_SHADING_RATE_Y_AXIS(y) ((y) & D3D12_SHADING_RATE_VALID_MASK)")

typedef enum D3D12_SHADING_RATE
{
    D3D12_SHADING_RATE_1X1 = 0x0,
    D3D12_SHADING_RATE_1X2 = 0x1,
    D3D12_SHADING_RATE_2X1 = 0x4,
    D3D12_SHADING_RATE_2X2 = 0x5,
    D3D12_SHADING_RATE_2X4 = 0x6,
    D3D12_SHADING_RATE_4X2 = 0x9,
    D3D12_SHADING_RATE_4X4 = 0xA
} D3D12_SHADING_RATE;

typedef enum D3D12_SHADING_RATE_COMBINER
{
    D3D12_SHADING_RATE_COMBINER_PASSTHROUGH = 0,
    D3D12_SHADING_RATE_COMBINER_OVERRIDE = 1,
    D3D12_SHADING_RATE_COMBINER_MIN = 2,
    D3D12_SHADING_RATE_COMBINER_MAX = 3,
    D3D12_SHADING_RATE_COMBINER_SUM = 4,
} D3D12_SHADING_RATE_COMBINER;

[uuid(*************-474c-87f5-6472eaee44ea), object, local, pointer_default(unique)]
interface ID3D12GraphicsCommandList5 : ID3D12GraphicsCommandList4
{
    void RSSetShadingRate(
        [annotation("_In_")] D3D12_SHADING_RATE baseShadingRate,
        [annotation("_In_reads_opt_(D3D12_RS_SET_SHADING_RATE_COMBINER_COUNT)")] const D3D12_SHADING_RATE_COMBINER* combiners);

    void RSSetShadingRateImage(
        [annotation("_In_opt_")] ID3D12Resource* shadingRateImage);
}

typedef struct D3D12_DISPATCH_MESH_ARGUMENTS
{
    UINT ThreadGroupCountX;
    UINT ThreadGroupCountY;
    UINT ThreadGroupCountZ;
} D3D12_DISPATCH_MESH_ARGUMENTS;

[uuid(c3827890-e548-4cfa-96cf-5689a9370f80), object, local, pointer_default(unique)]
interface ID3D12GraphicsCommandList6 : ID3D12GraphicsCommandList5
{
    void DispatchMesh(
        [annotation("_In_")] UINT ThreadGroupCountX,
        [annotation("_In_")] UINT ThreadGroupCountY,
        [annotation("_In_")] UINT ThreadGroupCountZ
        );
}

[uuid(dd171223-8b61-4769-90e3-160ccde4e2c1), object, local, pointer_default(unique)]
interface ID3D12GraphicsCommandList7 : ID3D12GraphicsCommandList6
{
    void Barrier(
        UINT32 NumBarrierGroups,
        [annotation("_In_reads_(NumBarrierGroups)")] const D3D12_BARRIER_GROUP *pBarrierGroups
        );
};

[uuid(ee936ef9-599d-4d28-938e-23c4ad05ce51), object, local, pointer_default(unique)]
interface ID3D12GraphicsCommandList8 : ID3D12GraphicsCommandList7
{
    void OMSetFrontAndBackStencilRef(
        [annotation("_In_")] UINT FrontStencilRef,
        [annotation("_In_")] UINT BackStencilRef
    );
};

[uuid(34ed2808-ffe6-4c2b-b11a-cabd2b0c59e1), object, local, pointer_default(unique)]
interface ID3D12GraphicsCommandList9 : ID3D12GraphicsCommandList8
{
    void RSSetDepthBias(
        [annotation("_In_")] FLOAT DepthBias,
        [annotation("_In_")] FLOAT DepthBiasClamp,
        [annotation("_In_")] FLOAT SlopeScaledDepthBias
    );

    void IASetIndexBufferStripCutValue(
        [annotation("_In_")] D3D12_INDEX_BUFFER_STRIP_CUT_VALUE IBStripCutValue
    );
};

[uuid(7013c015-d161-4b63-a08c-238552dd8acc), object, local, pointer_default(unique)]
interface ID3D12GraphicsCommandList10 : ID3D12GraphicsCommandList9
{
    void SetProgram([annotation("_In_")] const D3D12_SET_PROGRAM_DESC* pDesc);
    void DispatchGraph([annotation("_In_")] const D3D12_DISPATCH_GRAPH_DESC* pDesc);
};


[uuid(f343d1a0-afe3-439f-b13d-cd87a43b70ca), object, local, pointer_default(unique)]
interface ID3D12DSRDeviceFactory : IUnknown
{
    HRESULT CreateDSRDevice(
        [in] ID3D12Device *pD3D12Device,
        [in] UINT NodeMask,
        [in] REFIID riid, // Expected IDSRDevice
        [out, iid_is(riid), annotation("_COM_Outptr_")] void** ppvDSRDevice);
}

[uuid(597985ab-9b75-4dbb-be23-0761195bebee), object, local, pointer_default(unique)]
interface ID3D12GBVDiagnostics
    : IUnknown
{
    HRESULT GetGBVEntireSubresourceStatesData( [annotation("_In_")] ID3D12Resource* pResource, [annotation("_Out_writes_bytes_(DataSize)")] int *pData, UINT DataSize );
    HRESULT GetGBVSubresourceState( [annotation("_In_")] ID3D12Resource* pResource, UINT Subresource, [annotation("_Out_")] int *pData );
    HRESULT GetGBVResourceUniformState( [annotation("_In_")] ID3D12Resource* pResource, [annotation("_Out_")] int *pData );
    HRESULT GetGBVResourceInfo( [annotation("_In_")] ID3D12Resource* pResource, 
                               [annotation("_In_opt_")] D3D12_RESOURCE_DESC* pResourceDesc,
                               [annotation("_In_opt_")] UINT32* pResourceHash,
                               [annotation("_In_opt_")] UINT32* pSubresourceStatesByteOffset );

    void GBVReserved0();

    void GBVReserved1();
}

//----------------------------------------------------------------------------------------------------------
// Old types which are still supported by the runtime for app-compat

cpp_quote("#endif /* WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_APP | WINAPI_PARTITION_GAMES) */")
#pragma endregion

cpp_quote( "DEFINE_GUID(IID_ID3D12Object,0xc4fec28f,0x7966,0x4e95,0x9f,0x94,0xf4,0x31,0xcb,0x56,0xc3,0xb8);" )
cpp_quote( "DEFINE_GUID(IID_ID3D12DeviceChild,0x905db94b,0xa00c,0x4140,0x9d,0xf5,0x2b,0x64,0xca,0x9e,0xa3,0x57);" )
cpp_quote( "DEFINE_GUID(IID_ID3D12RootSignature,0xc54a6b66,0x72df,0x4ee8,0x8b,0xe5,0xa9,0x46,0xa1,0x42,0x92,0x14);" )
cpp_quote( "DEFINE_GUID(IID_ID3D12RootSignatureDeserializer,0x34AB647B,0x3CC8,0x46AC,0x84,0x1B,0xC0,0x96,0x56,0x45,0xC0,0x46);" )
cpp_quote( "DEFINE_GUID(IID_ID3D12VersionedRootSignatureDeserializer,0x7F91CE67,0x090C,0x4BB7,0xB7,0x8E,0xED,0x8F,0xF2,0xE3,0x1D,0xA0);" )
cpp_quote( "DEFINE_GUID(IID_ID3D12Pageable,0x63ee58fb,0x1268,0x4835,0x86,0xda,0xf0,0x08,0xce,0x62,0xf0,0xd6);" )
cpp_quote( "DEFINE_GUID(IID_ID3D12Heap,0x6b3b2502,0x6e51,0x45b3,0x90,0xee,0x98,0x84,0x26,0x5e,0x8d,0xf3);" )
cpp_quote( "DEFINE_GUID(IID_ID3D12Resource,0x696442be,0xa72e,0x4059,0xbc,0x79,0x5b,0x5c,0x98,0x04,0x0f,0xad);" )
cpp_quote( "DEFINE_GUID(IID_ID3D12CommandAllocator,0x6102dee4,0xaf59,0x4b09,0xb9,0x99,0xb4,0x4d,0x73,0xf0,0x9b,0x24);" )
cpp_quote( "DEFINE_GUID(IID_ID3D12Fence,0x0a753dcf,0xc4d8,0x4b91,0xad,0xf6,0xbe,0x5a,0x60,0xd9,0x5a,0x76);" )
cpp_quote( "DEFINE_GUID(IID_ID3D12Fence1,0x433685fe,0xe22b,0x4ca0,0xa8,0xdb,0xb5,0xb4,0xf4,0xdd,0x0e,0x4a);" )
cpp_quote( "DEFINE_GUID(IID_ID3D12PipelineState,0x765a30f3,0xf624,0x4c6f,0xa8,0x28,0xac,0xe9,0x48,0x62,0x24,0x45);" )
cpp_quote( "DEFINE_GUID(IID_ID3D12DescriptorHeap,0x8efb471d,0x616c,0x4f49,0x90,0xf7,0x12,0x7b,0xb7,0x63,0xfa,0x51);" )
cpp_quote( "DEFINE_GUID(IID_ID3D12QueryHeap,0x0d9658ae,0xed45,0x469e,0xa6,0x1d,0x97,0x0e,0xc5,0x83,0xca,0xb4);" )
cpp_quote( "DEFINE_GUID(IID_ID3D12CommandSignature,0xc36a797c,0xec80,0x4f0a,0x89,0x85,0xa7,0xb2,0x47,0x50,0x82,0xd1);" )
cpp_quote( "DEFINE_GUID(IID_ID3D12CommandList,0x7116d91c,0xe7e4,0x47ce,0xb8,0xc6,0xec,0x81,0x68,0xf4,0x37,0xe5);" )
cpp_quote( "DEFINE_GUID(IID_ID3D12GraphicsCommandList,0x5b160d0f,0xac1b,0x4185,0x8b,0xa8,0xb3,0xae,0x42,0xa5,0xa4,0x55);" )
cpp_quote( "DEFINE_GUID(IID_ID3D12GraphicsCommandList1,0x553103fb,0x1fe7,0x4557,0xbb,0x38,0x94,0x6d,0x7d,0x0e,0x7c,0xa7);" )
cpp_quote( "DEFINE_GUID(IID_ID3D12GraphicsCommandList2,0x38C3E585,0xFF17,0x412C,0x91,0x50,0x4F,0xC6,0xF9,0xD7,0x2A,0x28);" )
cpp_quote( "DEFINE_GUID(IID_ID3D12CommandQueue,0x0ec870a6,0x5d7e,0x4c22,0x8c,0xfc,0x5b,0xaa,0xe0,0x76,0x16,0xed);" )
cpp_quote( "DEFINE_GUID(IID_ID3D12Device,0x189819f1,0x1db6,0x4b57,0xbe,0x54,0x18,0x21,0x33,0x9b,0x85,0xf7);" )
cpp_quote( "DEFINE_GUID(IID_ID3D12PipelineLibrary,0xc64226a8,0x9201,0x46af,0xb4,0xcc,0x53,0xfb,0x9f,0xf7,0x41,0x4f);" )
cpp_quote( "DEFINE_GUID(IID_ID3D12PipelineLibrary1,0x80eabf42,0x2568,0x4e5e,0xbd,0x82,0xc3,0x7f,0x86,0x96,0x1d,0xc3);" )
cpp_quote( "DEFINE_GUID(IID_ID3D12Device1,0x77acce80,0x638e,0x4e65,0x88,0x95,0xc1,0xf2,0x33,0x86,0x86,0x3e);" )
cpp_quote( "DEFINE_GUID(IID_ID3D12Device2,0x30baa41e,0xb15b,0x475c,0xa0,0xbb,0x1a,0xf5,0xc5,0xb6,0x43,0x28);" )
cpp_quote( "DEFINE_GUID(IID_ID3D12Device3,0x81dadc15,0x2bad,0x4392,0x93,0xc5,0x10,0x13,0x45,0xc4,0xaa,0x98);" )
cpp_quote( "DEFINE_GUID(IID_ID3D12ProtectedSession,0xA1533D18,0x0AC1,0x4084,0x85,0xB9,0x89,0xA9,0x61,0x16,0x80,0x6B);" )
cpp_quote( "DEFINE_GUID(IID_ID3D12ProtectedResourceSession,0x6CD696F4,0xF289,0x40CC,0x80,0x91,0x5A,0x6C,0x0A,0x09,0x9C,0x3D);" )
cpp_quote( "DEFINE_GUID(IID_ID3D12Device4,0xe865df17,0xa9ee,0x46f9,0xa4,0x63,0x30,0x98,0x31,0x5a,0xa2,0xe5);" )
cpp_quote( "DEFINE_GUID(IID_ID3D12LifetimeOwner,0xe667af9f,0xcd56,0x4f46,0x83,0xce,0x03,0x2e,0x59,0x5d,0x70,0xa8);" )
cpp_quote( "DEFINE_GUID(IID_ID3D12SwapChainAssistant,0xf1df64b6,0x57fd,0x49cd,0x88,0x07,0xc0,0xeb,0x88,0xb4,0x5c,0x8f);" )
cpp_quote( "DEFINE_GUID(IID_ID3D12LifetimeTracker,0x3fd03d36,0x4eb1,0x424a,0xa5,0x82,0x49,0x4e,0xcb,0x8b,0xa8,0x13);" )
cpp_quote( "DEFINE_GUID(IID_ID3D12StateObject,0x47016943,0xfca8,0x4594,0x93,0xea,0xaf,0x25,0x8b,0x55,0x34,0x6d);" )
cpp_quote( "DEFINE_GUID(IID_ID3D12StateObjectProperties,0xde5fa827,0x9bf9,0x4f26,0x89,0xff,0xd7,0xf5,0x6f,0xde,0x38,0x60);" )
cpp_quote( "DEFINE_GUID(IID_ID3D12StateObjectProperties1,0x460caac7,0x1d24,0x446a,0xa1,0x84,0xca,0x67,0xdb,0x49,0x41,0x38);" )
cpp_quote( "DEFINE_GUID(IID_ID3D12WorkGraphProperties,0x065acf71,0xf863,0x4b89,0x82,0xf4,0x02,0xe4,0xd5,0x88,0x67,0x57);" )
cpp_quote( "DEFINE_GUID(IID_ID3D12Device5,0x8b4f173b,0x2fea,0x4b80,0x8f,0x58,0x43,0x07,0x19,0x1a,0xb9,0x5d);" )
cpp_quote( "DEFINE_GUID(IID_ID3D12DeviceRemovedExtendedDataSettings,0x82BC481C,0x6B9B,0x4030,0xAE,0xDB,0x7E,0xE3,0xD1,0xDF,0x1E,0x63);" )
cpp_quote( "DEFINE_GUID(IID_ID3D12DeviceRemovedExtendedDataSettings1,0xDBD5AE51,0x3317,0x4F0A,0xAD,0xF9,0x1D,0x7C,0xED,0xCA,0xAE,0x0B);" )
cpp_quote( "DEFINE_GUID(IID_ID3D12DeviceRemovedExtendedDataSettings2,0x61552388,0x01ab,0x4008,0xa4,0x36,0x83,0xdb,0x18,0x95,0x66,0xea);" )
cpp_quote( "DEFINE_GUID(IID_ID3D12DeviceRemovedExtendedData,0x98931D33,0x5AE8,0x4791,0xAA,0x3C,0x1A,0x73,0xA2,0x93,0x4E,0x71);" )
cpp_quote( "DEFINE_GUID(IID_ID3D12DeviceRemovedExtendedData1,0x9727A022,0xCF1D,0x4DDA,0x9E,0xBA,0xEF,0xFA,0x65,0x3F,0xC5,0x06);" )
cpp_quote( "DEFINE_GUID(IID_ID3D12DeviceRemovedExtendedData2,0x67FC5816,0xE4CA,0x4915,0xBF,0x18,0x42,0x54,0x12,0x72,0xDA,0x54);" )
cpp_quote( "DEFINE_GUID(IID_ID3D12Device6,0xc70b221b,0x40e4,0x4a17,0x89,0xaf,0x02,0x5a,0x07,0x27,0xa6,0xdc);" )
cpp_quote( "DEFINE_GUID(IID_ID3D12ProtectedResourceSession1,0xD6F12DD6,0x76FB,0x406E,0x89,0x61,0x42,0x96,0xEE,0xFC,0x04,0x09);" )
cpp_quote( "DEFINE_GUID(IID_ID3D12Device7,0x5c014b53,0x68a1,0x4b9b,0x8b,0xd1,0xdd,0x60,0x46,0xb9,0x35,0x8b);" )
cpp_quote( "DEFINE_GUID(IID_ID3D12Device8,0x9218E6BB,0xF944,0x4F7E,0xA7,0x5C,0xB1,0xB2,0xC7,0xB7,0x01,0xF3);" )
cpp_quote( "DEFINE_GUID(IID_ID3D12Resource1,0x9D5E227A,0x4430,0x4161,0x88,0xB3,0x3E,0xCA,0x6B,0xB1,0x6E,0x19);" )
cpp_quote( "DEFINE_GUID(IID_ID3D12Resource2,0xBE36EC3B,0xEA85,0x4AEB,0xA4,0x5A,0xE9,0xD7,0x64,0x04,0xA4,0x95);" )
cpp_quote( "DEFINE_GUID(IID_ID3D12Heap1,0x572F7389,0x2168,0x49E3,0x96,0x93,0xD6,0xDF,0x58,0x71,0xBF,0x6D);" )
cpp_quote( "DEFINE_GUID(IID_ID3D12GraphicsCommandList3,0x6FDA83A7,0xB84C,0x4E38,0x9A,0xC8,0xC7,0xBD,0x22,0x01,0x6B,0x3D);" )
cpp_quote( "DEFINE_GUID(IID_ID3D12MetaCommand,0xDBB84C27,0x36CE,0x4FC9,0xB8,0x01,0xF0,0x48,0xC4,0x6A,0xC5,0x70);" )
cpp_quote( "DEFINE_GUID(IID_ID3D12GraphicsCommandList4,0x8754318e,0xd3a9,0x4541,0x98,0xcf,0x64,0x5b,0x50,0xdc,0x48,0x74);" )
cpp_quote( "DEFINE_GUID(IID_ID3D12ShaderCacheSession,0x28e2495d,0x0f64,0x4ae4,0xa6,0xec,0x12,0x92,0x55,0xdc,0x49,0xa8);" )
cpp_quote( "DEFINE_GUID(IID_ID3D12Device9,0x4c80e962,0xf032,0x4f60,0xbc,0x9e,0xeb,0xc2,0xcf,0xa1,0xd8,0x3c);" )
cpp_quote( "DEFINE_GUID(IID_ID3D12Device10,0x517f8718,0xaa66,0x49f9,0xb0,0x2b,0xa7,0xab,0x89,0xc0,0x60,0x31);" )
cpp_quote( "DEFINE_GUID(IID_ID3D12Device11,0x5405c344,0xd457,0x444e,0xb4,0xdd,0x23,0x66,0xe4,0x5a,0xee,0x39);" )
cpp_quote( "DEFINE_GUID(IID_ID3D12Device12,0x5af5c532,0x4c91,0x4cd0,0xb5,0x41,0x15,0xa4,0x05,0x39,0x5f,0xc5);" )
cpp_quote( "DEFINE_GUID(IID_ID3D12Device13,0x14eecffc,0x4df8,0x40f7,0xa1,0x18,0x5c,0x81,0x6f,0x45,0x69,0x5e);" )
cpp_quote( "DEFINE_GUID(IID_ID3D12Device14,0x5f6e592d,0xd895,0x44c2,0x8e,0x4a,0x88,0xad,0x49,0x26,0xd3,0x23);" )
cpp_quote( "DEFINE_GUID(IID_ID3D12VirtualizationGuestDevice,0xbc66d368,0x7373,0x4943,0x87,0x57,0xfc,0x87,0xdc,0x79,0xe4,0x76);" )
cpp_quote( "DEFINE_GUID(IID_ID3D12Tools,0x7071e1f0,0xe84b,0x4b33,0x97,0x4f,0x12,0xfa,0x49,0xde,0x65,0xc5);" )
cpp_quote( "DEFINE_GUID(IID_ID3D12SDKConfiguration,0xe9eb5314,0x33aa,0x42b2,0xa7,0x18,0xd7,0x7f,0x58,0xb1,0xf1,0xc7);" )
cpp_quote( "DEFINE_GUID(IID_ID3D12SDKConfiguration1,0x8aaf9303,0xad25,0x48b9,0x9a,0x57,0xd9,0xc3,0x7e,0x00,0x9d,0x9f);" )
cpp_quote( "DEFINE_GUID(IID_ID3D12DeviceFactory,0x61f307d3,0xd34e,0x4e7c,0x83,0x74,0x3b,0xa4,0xde,0x23,0xcc,0xcb);" )
cpp_quote( "DEFINE_GUID(IID_ID3D12DeviceConfiguration,0x78dbf87b,0xf766,0x422b,0xa6,0x1c,0xc8,0xc4,0x46,0xbd,0xb9,0xad);" )
cpp_quote( "DEFINE_GUID(IID_ID3D12DeviceConfiguration1,0xed342442,0x6343,0x4e16,0xbb,0x82,0xa3,0xa5,0x77,0x87,0x4e,0x56);" )
cpp_quote( "DEFINE_GUID(IID_ID3D12GraphicsCommandList5,0x55050859,0x4024,0x474c,0x87,0xf5,0x64,0x72,0xea,0xee,0x44,0xea);" )
cpp_quote( "DEFINE_GUID(IID_ID3D12GraphicsCommandList6,0xc3827890,0xe548,0x4cfa,0x96,0xcf,0x56,0x89,0xa9,0x37,0x0f,0x80);" )
cpp_quote( "DEFINE_GUID(IID_ID3D12GraphicsCommandList7,0xdd171223,0x8b61,0x4769,0x90,0xe3,0x16,0x0c,0xcd,0xe4,0xe2,0xc1);" )
cpp_quote( "DEFINE_GUID(IID_ID3D12GraphicsCommandList8,0xee936ef9,0x599d,0x4d28,0x93,0x8e,0x23,0xc4,0xad,0x05,0xce,0x51);" )
cpp_quote( "DEFINE_GUID(IID_ID3D12GraphicsCommandList9,0x34ed2808,0xffe6,0x4c2b,0xb1,0x1a,0xca,0xbd,0x2b,0x0c,0x59,0xe1);" )
cpp_quote( "DEFINE_GUID(IID_ID3D12GraphicsCommandList10,0x7013c015,0xd161,0x4b63,0xa0,0x8c,0x23,0x85,0x52,0xdd,0x8a,0xcc);" )
cpp_quote( "DEFINE_GUID(IID_ID3D12DSRDeviceFactory,0xf343d1a0,0xafe3,0x439f,0xb1,0x3d,0xcd,0x87,0xa4,0x3b,0x70,0xca);" )
cpp_quote( "DEFINE_GUID(IID_ID3D12GBVDiagnostics,0x597985ab,0x9b75,0x4dbb,0xbe,0x23,0x07,0x61,0x19,0x5b,0xeb,0xee);" )
