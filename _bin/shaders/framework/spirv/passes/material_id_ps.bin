NVSP   `
  ALPHA_TESTED=0#     )                      	       main                             �       type.c_Material          g_Material       MaterialConstants            baseOrDiffuseColor          flags           specularColor           materialID          emissiveColor           domain          opacity         roughness           metalness        	   normalTextureScale       
   occlusionStrength           alphaCutoff         transmissionFactor   
    
   baseOrDiffuseTextureIndex           metalRoughOrSpecularTextureIndex     	       emissiveTextureIndex            normalTextureIndex   	       occlusionTextureIndex    
       transmissionTextureIndex            opacityTextureIndex  
       normalTextureTransformScale         padding1            sssScale     	       sssTransmissionColor            sssAnisotropy           sssScatteringColor          hairMelanin         hairBaseColor           hairMelaninRedness   
       hairLongitudinalRoughness    	       hairAzimuthalRoughness          hairIor          hairCuticleAngle     
    !   hairDiffuseReflectionTint    
    "   hairDiffuseReflectionWeight      c_Material   
    type.ConstantBuffer.GBufferPushConstants     	        startInstanceLocation           startVertexLocation         positionOffset          prevPositionOffset          texCoordOffset          normalOffset            tangentOffset        g_Push       in.var.INSTANCE      out.var.SV_Target0       main    G        G           G            G     "       G     !      H         #       H        #      H        #      H        #      H        #       H        #   ,   H        #   0   H        #   4   H        #   8   H     	   #   <   H     
   #   @   H        #   D   H        #   H   H     
   #   L   H        #   P   H        #   T   H        #   X   H        #   \   H        #   `   H        #   d   H        #   h   H        #   p   H        #   |   H        #   �   H        #   �   H        #   �   H        #   �   H        #   �   H        #   �   H        #   �   H        #   �   H        #   �   H         #   �   H     !   #   �   H     "   #   �   H         #       G        H         #       H        #      H        #      H        #      H        #      H        #      H        #      G          	          +  	   
       +  	                    
           +  
               
      ,                                         
       %       	      	      	                        	   	   	   	   	   	   	                                                                     	    
   
   
   
   
   
   
         	               
        
                       !                 	         	   
   ;           ;        	   ;           ;                   6               �     =  
         A            
      =  	   !       |  
   "   !   R     #   "          A     $      
   =  
   %   $   �  
   &   %      R     '   &   #      O 	    (   '                   >     (   �  8     ,  ALPHA_TESTED=1#     q                 GLSL.std.450              
       main                      	   
                �       type.c_Material          g_Material       MaterialConstants            baseOrDiffuseColor          flags           specularColor           materialID          emissiveColor           domain          opacity         roughness           metalness        	   normalTextureScale       
   occlusionStrength           alphaCutoff         transmissionFactor   
    
   baseOrDiffuseTextureIndex           metalRoughOrSpecularTextureIndex     	       emissiveTextureIndex            normalTextureIndex   	       occlusionTextureIndex    
       transmissionTextureIndex            opacityTextureIndex  
       normalTextureTransformScale         padding1            sssScale     	       sssTransmissionColor            sssAnisotropy           sssScatteringColor          hairMelanin         hairBaseColor           hairMelaninRedness   
       hairLongitudinalRoughness    	       hairAzimuthalRoughness          hairIor          hairCuticleAngle     
    !   hairDiffuseReflectionTint    
    "   hairDiffuseReflectionWeight      c_Material    
   type.2d.image        t_BaseOrDiffuse      t_Opacity        type.sampler      	   s_MaterialSampler    
    type.ConstantBuffer.GBufferPushConstants     	        startInstanceLocation           startVertexLocation         positionOffset          prevPositionOffset          texCoordOffset          normalOffset            tangentOffset     
   g_Push       in.var.TEXCOORD      in.var.INSTANCE      out.var.SV_Target0       main         type.sampled.image  G        G           G           G            G     "       G     !      G     "       G     !       G     "       G     !      G  	   "      G  	   !   �   H         #       H        #      H        #      H        #      H        #       H        #   ,   H        #   0   H        #   4   H        #   8   H     	   #   <   H     
   #   @   H        #   D   H        #   H   H     
   #   L   H        #   P   H        #   T   H        #   X   H        #   \   H        #   `   H        #   d   H        #   h   H        #   p   H        #   |   H        #   �   H        #   �   H        #   �   H        #   �   H        #   �   H        #   �   H        #   �   H        #   �   H        #   �   H         #   �   H     !   #   �   H     "   #   �   H         #       G        H         #       H        #      H        #      H        #      H        #      H        #      H        #      G                    +            +           +           +           +                    +           +                         +                       ,              +          �?                   !         ,  !   "                 #           $          %                                                                   #   $                                                         %          	 
                              &       
           '           	                            (   	         )      #      *           +            ,      +     -   !  .   -      /            0            1            2   	           
   ;  %         ;  &          ;  &          ;  '   	       ;  (   
   	   ;  )         ;  *         ;  ,           +   3   +     4      +     5      6  -          .   �  6   =  #   7      =     8      A  /   9         A  1   :            =     ;   :   �     <   ;      �     =   <      �  >       �  =   ?   >   �  ?   =  
   @      =     A   	   V     B   @   A   W  !   C   B   7       �  >   �  >   �  !   D   "   6   C   ?   �     E   ;      �     F   E      �  G       �  F   H   G   �  H   =  
   I      =     J   	   V     K   I   J   W  !   L   K   7       Q     M   L       �  G   �  G   �     N      >   M   H   A  1   O   9   4   =     P   O   A  0   Q   9   5   =     R   Q   �     S   P      �     T   S      �  U       �  T   V   W   �  V   �     X   R   N   �  U   �  W   �     Y   P      �     Z   Y      �  [       �  Z   \   [   �  \   Q     ]   D      �     ^   R   ]   �  [   �  [   �     _   R   W   ^   \   �  U   �  U   �     `   X   V   _   [        a      +   `         A  0   b            =     c   b   �     d   a   c   �     e   d      �  f       �  e   g   f   �  g   �  �  f   A  1   h            =     i   h   |     j   i   R  +   k   j   3       A  2   l   
      =     m   l   �     n   m   8   R  +   o   n   k      O 	 +   p   o                   >     p   �  8  