NVSP   �  MOTION_VECTORS=0#     �                 GLSL.std.450                      buffer_loads                      	   
         
              �   
    type.ConstantBuffer.GBufferFillConstants             view            viewPrev         PlanarViewConstants          matWorldToView          matViewToClip           matWorldToClip          matClipToView           matViewToWorld          matClipToWorld   	       matViewToClipNoOffset    	       matWorldToClipNoOffset   	       matClipToViewNoOffset    	    	   matClipToWorldNoOffset       
   viewportOrigin          viewportSize            viewportSizeInv      
   pixelOffset         clipToWindowScale           clipToWindowBias            windowToClipScale           windowToClipBias     
       cameraDirectionOrPosition        c_GBuffer        type.StructuredBuffer.InstanceData       InstanceData             flags    
       firstGeometryInstanceIndex          firstGeometryIndex          numGeometries           transform           prevTransform     
   t_Instances      type.ByteAddressBuffer       t_Vertices   
    type.ConstantBuffer.GBufferPushConstants     	        startInstanceLocation           startVertexLocation         positionOffset          prevPositionOffset          texCoordOffset          normalOffset            tangentOffset        g_Push       out.var.POS      out.var.PREV_POS         out.var.TEXCOORD      	   out.var.NORMAL    
   out.var.TANGENT      out.var.INSTANCE         buffer_loads    G        *   G        +   G            G  	      G  
      G            G           G           G  	         G  
         G           G     "      G     !     G  
   "      G  
   !   
   G     "      G     !      H         #       H               H            H        #   @   H              H           H        #   �   H              H           H        #   �   H              H           H        #      H              H           H        #   @  H              H           H        #   �  H              H           H        #   �  H              H           H        #      H              H           H     	   #   @  H     	         H     	      H     
   #   �  H        #   �  H        #   �  H     
   #   �  H        #   �  H        #   �  H        #   �  H        #   �  H        #   �  H         #       H        #   �  G        H         #       H        #      H        #      H        #      H        #      H              H           H        #   @   H              H           G        p   H         #       H            G        G           H         #       H            G        H         #       H        #      H        #      H        #      H        #      H        #      H        #      G                    +            +           +           +           +                        +           +            +     !      +     "      +     #      +     $      +     %       +     &        '       +  '   (     �?+  '   )       +     *      +     +      +  '   ,     ��  -   '        .   -        /   '           .   .   .   .   .   .   .   .   .   .   /   /   /   /   /   /   /   /   -                 0           1   -                       1   1                      2                            3          	                            4   	         5            6      -     7   '         8      7      9      /      :           ;   !  <   ;      =   	         >            ?           @           A            B      .   ;  0         ;  2   
      ;  3         ;  4      	   ;  5         ;  5         ;  6         ;  8         ;  8         ;  9         ;  8   	      ;  6   
      ;  :           -   C   +  '   D   <6  ;          <   �  E   =     F      =     G      A  =   H         =     I   H   �     J   G   I   A  =   K         =     L   K   �     M   F   L   A  >   N   
      J   =     O   N   Q  1   P   O      A  =   Q         =     R   Q   �     S   M       �     T   R   S   �     U   T   $   A  ?   V      %   U   =     W   V   �     X   U      A  ?   Y      %   X   =     Z   Y   �     [   U   $   A  ?   \      %   [   =     ]   \   P  @   ^   W   Z   ]   |  7   _   ^   A  =   `         =     a   `   �     b   M   !   �     c   a   b   �     d   c   $   A  ?   e      %   d   =     f   e   �     g   d      A  ?   h      %   g   =     i   h   P  A   j   f   i   |  /   k   j   A  =   l         =     m   l   �     n   M   "   �     o   m   n   �     p   o   $   A  ?   q      %   p   =     r   q   A  =   s      &   =     t   s   �     u   t   n   �     v   u   $   A  ?   w      %   v   =     x   w   �     y   r   *   |     z   y   �     {   z   +   o  '   |   {   �  '   }   |   D     '   ~      +   }   ,   (   �        r   !   �     �      *   |     �   �   �     �   �   +   o  '   �   �   �  '   �   �   D     '   �      +   �   ,   (   �     �   r   #   �     �   �   *   |     �   �   �     �   �   +   o  '   �   �   �  '   �   �   D     '   �      +   �   ,   (   �     �   x   *   |     �   �   �     �   �   +   o  '   �   �   �  '   �   �   D     '   �      +   �   ,   (   �     �   x   !   �     �   �   *   |     �   �   �     �   �   +   o  '   �   �   �  '   �   �   D     '   �      +   �   ,   (   �     �   x   #   �     �   �   *   |     �   �   �     �   �   +   o  '   �   �   �  '   �   �   D     '   �      +   �   ,   (   �     �   x   *   �     �   �   *   |     �   �   �     �   �   +   o  '   �   �   �  '   �   �   D     '   �      +   �   ,   (   Q  '   �   _       Q  '   �   _      Q  '   �   _      P  -   �   �   �   �   (   �  7   �   �   P   P  -   �   ~   �   �   )   �  7   �   �   P   P  -   �   �   �   �   )   �  7   �   �   P   O 	 -   �   C   �               R  -   �   �   �      Q  '   �   �       Q  '   �   �      Q  '   �   �      P  -   �   �   �   �   (   A  B   �            =  .   �   �   �  -   �   �   �   >     �   >     �   >     �   >     k   >  	   �   >  
   �   >     G   �  8     p  MOTION_VECTORS=1#     �                 GLSL.std.450                      buffer_loads                      	   
         
                 �   
    type.ConstantBuffer.GBufferFillConstants             view            viewPrev         PlanarViewConstants          matWorldToView          matViewToClip           matWorldToClip          matClipToView           matViewToWorld          matClipToWorld   	       matViewToClipNoOffset    	       matWorldToClipNoOffset   	       matClipToViewNoOffset    	    	   matClipToWorldNoOffset       
   viewportOrigin          viewportSize            viewportSizeInv      
   pixelOffset         clipToWindowScale           clipToWindowBias            windowToClipScale           windowToClipBias     
       cameraDirectionOrPosition     
   c_GBuffer        type.StructuredBuffer.InstanceData       InstanceData             flags    
       firstGeometryInstanceIndex          firstGeometryIndex          numGeometries           transform           prevTransform        t_Instances      type.ByteAddressBuffer       t_Vertices   
    type.ConstantBuffer.GBufferPushConstants     	        startInstanceLocation           startVertexLocation         positionOffset          prevPositionOffset          texCoordOffset          normalOffset            tangentOffset        g_Push       out.var.POS      out.var.PREV_POS         out.var.TEXCOORD      	   out.var.NORMAL    
   out.var.TANGENT      out.var.PREV_WORLD_POS       out.var.INSTANCE         buffer_loads    G        *   G        +   G            G  	      G  
      G            G           G           G  	         G  
         G           G           G  
   "      G  
   !     G     "      G     !   
   G     "      G     !      H         #       H               H            H        #   @   H              H           H        #   �   H              H           H        #   �   H              H           H        #      H              H           H        #   @  H              H           H        #   �  H              H           H        #   �  H              H           H        #      H              H           H     	   #   @  H     	         H     	      H     
   #   �  H        #   �  H        #   �  H     
   #   �  H        #   �  H        #   �  H        #   �  H        #   �  H        #   �  H         #       H        #   �  G        H         #       H        #      H        #      H        #      H        #      H              H           H        #   @   H              H           G        p   H         #       H            G        G           H         #       H            G        H         #       H        #      H        #      H        #      H        #      H        #      H        #      G                    +            +           +           +           +           +                         +      !      +      "      +      #      +      $      +      %      +      &      +      '       +     (        )       +  )   *     �?+  )   +       +      ,      +     -      +  )   .     ��  /   )        0   /        1   )           0   0   0   0   0   0   0   0   0   0   1   1   1   1   1   1   1   1   /                 2           3   /                           3   3                      4                             5          	                                   6   	         7             8      /     9   )         :      9      ;      1      <            =   !  >   =      ?   	          @            A            B            C             D      0   ;  2   
      ;  4         ;  5         ;  6      	   ;  7         ;  7         ;  8         ;  :         ;  :         ;  ;         ;  :   	      ;  8   
      ;  :         ;  <           /   E   +  )   F   <6  =          >   �  G   =      H      =      I      A  ?   J         =      K   J   �      L   I   K   A  ?   M         =      N   M   �      O   H   N   A  @   P         L   =     Q   P   Q  3   R   Q      Q  3   S   Q      A  ?   T         =      U   T   �      V   O   "   �      W   U   V   �      X   W   &   A  A   Y      '   X   =      Z   Y   �      [   X   !   A  A   \      '   [   =      ]   \   �      ^   X   &   A  A   _      '   ^   =      `   _   P  B   a   Z   ]   `   |  9   b   a   A  ?   c         =      d   c   �      e   d   V   �      f   e   &   A  A   g      '   f   =      h   g   �      i   f   !   A  A   j      '   i   =      k   j   �      l   f   &   A  A   m      '   l   =      n   m   P  B   o   h   k   n   |  9   p   o   A  ?   q         =      r   q   �      s   O   #   �      t   r   s   �      u   t   &   A  A   v      '   u   =      w   v   �      x   u   !   A  A   y      '   x   =      z   y   P  C   {   w   z   |  1   |   {   A  ?   }         =      ~   }   �         O   $   �      �   ~      �      �   �   &   A  A   �      '   �   =      �   �   A  ?   �      (   =      �   �   �      �   �      �      �   �   &   A  A   �      '   �   =      �   �   �      �   �   ,   |     �   �   �     �   �   -   o  )   �   �   �  )   �   �   F     )   �      +   �   .   *   �      �   �   #   �      �   �   ,   |     �   �   �     �   �   -   o  )   �   �   �  )   �   �   F     )   �      +   �   .   *   �      �   �   %   �      �   �   ,   |     �   �   �     �   �   -   o  )   �   �   �  )   �   �   F     )   �      +   �   .   *   �      �   �   ,   |     �   �   �     �   �   -   o  )   �   �   �  )   �   �   F     )   �      +   �   .   *   �      �   �   #   �      �   �   ,   |     �   �   �     �   �   -   o  )   �   �   �  )   �   �   F     )   �      +   �   .   *   �      �   �   %   �      �   �   ,   |     �   �   �     �   �   -   o  )   �   �   �  )   �   �   F     )   �      +   �   .   *   �      �   �   ,   �      �   �   ,   |     �   �   �     �   �   -   o  )   �   �   �  )   �   �   F     )   �      +   �   .   *   Q  )   �   b       Q  )   �   b      Q  )   �   b      P  /   �   �   �   �   *   �  9   �   �   R   P  /   �   �   �   �   +   �  9   �   �   R   P  /   �   �   �   �   +   �  9   �   �   R   O 	 /   �   E   �               R  /   �   �   �      Q  )   �   p       Q  )   �   p      Q  )   �   p      P  /   �   �   �   �   *   �  9   �   �   S   Q  )   �   �       Q  )   �   �      Q  )   �   �      P  /   �   �   �   �   *   A  D   �   
         =  0   �   �   �  /   �   �   �   >     �   >     �   >     �   >     |   >  	   �   >  
   �   >     I   �  8  