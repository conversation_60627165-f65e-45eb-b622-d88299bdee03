NVSP   0
  ALPHA_TESTED=0DXBC�d雄癖�!,谖S�   0
     4   �  �  �  �	  RDEF�     �      <    �� �  `  RD11<          (   $          |                             �                            c_Material c_g_Push |      �   �           �                    �       �      �      ����    ����    g_Material MaterialConstants baseOrDiffuseColor float3 �                              flags int                              J  specularColor materialID emissiveColor domain opacity float                              �  roughness metalness normalTextureScale occlusionStrength alphaCutoff transmissionFactor baseOrDiffuseTextureIndex metalRoughOrSpecularTextureIndex emissiveTextureIndex normalTextureIndex occlusionTextureIndex transmissionTextureIndex opacityTextureIndex normalTextureTransformScale float2 �                            �  padding1 uint3 �                            %  sssScale sssTransmissionColor sssAnisotropy sssScatteringColor hairMelanin hairBaseColor hairMelaninRedness hairLongitudinalRoughness hairAzimuthalRoughness hairIor hairCuticleAngle hairDiffuseReflectionTint hairDiffuseReflectionWeight          D  P     t        �  P     �         �  P  ,   �  �  0   �  �  4   �  �  8   �  �  <   �  �  @   
  �  D     �  H   ,  P  L   F  P  P   g  P  T   |  P  X   �  P  \   �  P  `   �  P  d   �  �  h     ,  p   P  �  |   Y     �   n  �  �   |     �   �  �  �   �     �   �  �  �   �  �  �   �  �  �   �  �  �   �  �  �        �      �  �       4   # <                  �   ,            <      ����    ����    g_Push GBufferPushConstants startInstanceLocation dword                              ^  startVertexLocation positionOffset prevPositionOffset texCoordOffset normalOffset tangentOffset H  d      �  d     �  d     �  d     �  d     �  d     �  d             �                  3  Microsoft (R) HLSL Shader Compiler 10.1 ISGN�         �                    �                    �                    �                    �                    �                    �                   SV_Position POS PREV_POS TEXCOORD NORMAL TANGENT INSTANCE OSGN,                               SV_Target SHEX�   P   (   j Y  F�         Y  F�        b     e  �        "      
    
�         6        :�         6  �      @                  >  STAT�                                                                                                                                                     �  ALPHA_TESTED=1DXBC珚�K=
,5︾�   �     4   P  D	  x	  �  RDEF          <    �� �  �  RD11<          (   $          �                            �            ����       
   �            ����      
                                                          s_MaterialSampler t_BaseOrDiffuse t_Opacity c_Material c_g_Push      L  �                �              t      �      l      ����    ����    g_Material MaterialConstants baseOrDiffuseColor float3 �                            �  flags int                              �  specularColor materialID emissiveColor domain opacity float                              6  roughness metalness normalTextureScale occlusionStrength alphaCutoff transmissionFactor baseOrDiffuseTextureIndex metalRoughOrSpecularTextureIndex emissiveTextureIndex normalTextureIndex occlusionTextureIndex transmissionTextureIndex opacityTextureIndex normalTextureTransformScale float2 �                            z  padding1 uint3 �                            �  sssScale sssTransmissionColor sssAnisotropy sssScatteringColor hairMelanin hairBaseColor hairMelaninRedness hairLongitudinalRoughness hairAzimuthalRoughness hairIor hairCuticleAngle hairDiffuseReflectionTint hairDiffuseReflectionWeight �  �      �  �        �       �       �      '  �  ,   .  <  0   `  <  4   j  <  8   t  <  <   �  <  @   �  <  D   �  <  H   �  �  L   �  �  P   �  �  T     �  X     �  \   1  �  `   J  �  d   ^  �  h   �  �  p   �  <  |   �  �  �   �  <  �     �  �     <  �   '  �  �   5  <  �   H  <  �   b  <  �   y  <  �   �  <  �   �  �  �   �  <  �       4   # �                    �            �      ����    ����    g_Push GBufferPushConstants startInstanceLocation dword                              �  startVertexLocation positionOffset prevPositionOffset texCoordOffset normalOffset tangentOffset �  �        �     (  �     7  �     J  �     Y  �     f  �             t                  �  Microsoft (R) HLSL Shader Compiler 10.1 ISGN�         �                    �                    �                    �                   �                    �                    �                   SV_Position POS PREV_POS TEXCOORD NORMAL TANGENT INSTANCE OSGN,                               SV_Target SHEXl  P   �   j Y  F�         Y  F�        Z   `     X  p     UU  X  p    UU  b 2    b     e  �      h       2      鰪          @                 
      E  嬄  �CU B      F    F{      `       6  B      @    �?         E  嬄  �CU �      F    杝     `       6  �      @    �?  8  �      �     �         7  
      
      *      
�         7  	            :      
         	      
      � �A          1        
      @      
 
        "      
    
�         6        :�         6  �      @                  >  STAT�                                                                                                                                            